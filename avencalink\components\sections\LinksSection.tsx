import { Button } from "@/components/ui/button";
import { useState, useCallback, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { getIconComponent } from "@/lib/iconUtils";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface LinkItem {
  classIcon: string;
  text: string;
  url: string;
}

interface LinksSectionProps {
  links: LinkItem[];
  isLoading?: boolean;
}

// Enhanced link type detection
const getLinkType = (
  url: string
): "external" | "email" | "phone" | "anchor" | "social" | "location" => {
  if (url.startsWith("mailto:")) return "email";
  if (url.startsWith("tel:")) return "phone";
  if (url.startsWith("#")) return "anchor";
  if (
    url.includes("instagram.com") ||
    url.includes("facebook.com") ||
    url.includes("youtube.com") ||
    url.includes("whatsapp") ||
    url.includes("linktr.ee")
  )
    return "social";
  if (url.includes("maps.") || url.includes("goo.gl")) return "location";
  return "external";
};

// Loading skeleton component
const LinkSkeleton = ({ index }: { index: number }) => (
  <div
    className="w-full h-14 sm:h-16 lg:h-18 bg-muted/50 rounded-2xl animate-pulse"
    style={{
      animationDelay: `${index * 0.1}s`,
      animationDuration: "1.5s",
    }}
  >
    <div className="flex items-center justify-center space-x-2 sm:space-x-3 h-full px-4 sm:px-6">
      <div className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 bg-muted rounded flex-shrink-0"></div>
      <div className="flex-1 h-4 bg-muted rounded max-w-[280px] mx-auto"></div>
    </div>
  </div>
);

const LinksSection = ({ links, isLoading = false }: LinksSectionProps) => {
  const [clickedIndex, setClickedIndex] = useState<number | null>(null);
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Enhanced link click handler with better error handling
  const handleLinkClick = useCallback(
    async (url: string, index: number, event: React.MouseEvent) => {
      // Prevent double clicks
      if (clickedIndex === index) return;

      setClickedIndex(index);

      try {
        if (
          url.startsWith("http") ||
          url.startsWith("mailto:") ||
          url.startsWith("tel:")
        ) {
          // Add analytics tracking if needed
          window.open(url, "_blank", "noopener,noreferrer");
        } else if (url.startsWith("#")) {
          // Enhanced anchor link handling
          event.preventDefault();
          const element = document.querySelector(url);
          if (element) {
            element.scrollIntoView({
              behavior: "smooth",
              block: "start",
              inline: "nearest",
            });
          } else {
            console.warn(`Anchor element ${url} not found`);
          }
        }
      } catch (error) {
        console.error("Error opening link:", error);
        // Could show a toast notification here
      } finally {
        // Reset click state after animation
        setTimeout(() => setClickedIndex(null), 300);
      }
    },
    [clickedIndex]
  );

  // Keyboard navigation
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent, index: number) => {
      switch (event.key) {
        case "ArrowDown": {
          event.preventDefault();
          const nextIndex = Math.min(index + 1, links.length - 1);
          buttonRefs.current[nextIndex]?.focus();
          setFocusedIndex(nextIndex);
          break;
        }
        case "ArrowUp": {
          event.preventDefault();
          const prevIndex = Math.max(index - 1, 0);
          buttonRefs.current[prevIndex]?.focus();
          setFocusedIndex(prevIndex);
          break;
        }
        case "Home": {
          event.preventDefault();
          buttonRefs.current[0]?.focus();
          setFocusedIndex(0);
          break;
        }
        case "End": {
          event.preventDefault();
          const lastIndex = links.length - 1;
          buttonRefs.current[lastIndex]?.focus();
          setFocusedIndex(lastIndex);
          break;
        }
      }
    },
    [links.length]
  );

  // Initialize button refs
  useEffect(() => {
    buttonRefs.current = buttonRefs.current.slice(0, links.length);
  }, [links.length]);

  if (isLoading) {
    return (
      <div
        className="space-y-3 sm:space-y-4 mb-8"
        role="status"
        aria-label="Carregando links"
      >
        {Array.from({ length: 6 }).map((_, index) => (
          <LinkSkeleton key={index} index={index} />
        ))}
      </div>
    );
  }

  return (
    <div
      className="space-y-3 sm:space-y-4 mb-8"
      role="navigation"
      aria-label="Links principais"
    >
      {links.map((link, index) => {
        const linkType = getLinkType(link.url);
        const isClicked = clickedIndex === index;
        const isFocused = focusedIndex === index;

        return (
          <div
            key={`${link.url}-${index}`}
            className="relative group/container"
          >
            <Button
              ref={(el) => {
                buttonRefs.current[index] = el;
              }}
              onClick={(e) => handleLinkClick(link.url, index, e)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onFocus={() => setFocusedIndex(index)}
              onBlur={() => setFocusedIndex(null)}
              className={cn(
                "w-full max-w-full min-h-16 h-auto sm:min-h-18 lg:min-h-20 py-2 sm:py-3 lg:py-4 bg-gradient-to-r from-primary to-primary/80 hover:scale-[1.01] transform transition-all duration-300 ease-out shadow-md hover:shadow-lg text-primary-foreground font-medium group relative overflow-hidden rounded-2xl",
                "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none",
                "active:scale-[0.98] active:shadow-sm",
                isClicked && "scale-[0.98] shadow-sm",
                isFocused && "ring-2 ring-ring ring-offset-2"
              )}
              style={{
                animationDelay: `${index * 0.1}s`,
                animationFillMode: "both",
              }}
              aria-label={`${link.text} - ${
                linkType === "external"
                  ? "Abre em nova aba"
                  : linkType === "social"
                  ? "Rede social"
                  : linkType === "location"
                  ? "Localização"
                  : "Link interno"
              }`}
              aria-describedby={`link-description-${index}`}
            >
              {/* Enhanced background gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Shimmer effect on hover */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-transform duration-700 ease-out"></div>

              <div className="flex items-center justify-center w-full min-w-0 px-1 sm:px-2 lg:px-4 relative z-10">
                {/* Main content - Icon and text with proper overflow handling */}
                <div className="flex items-center space-x-1 sm:space-x-2 min-w-0 w-full overflow-hidden">
                  <div className="relative flex-shrink-0">
                    {(() => {
                      const IconComponent = getIconComponent(link.classIcon);
                      return (
                        <IconComponent
                          className="text-sm sm:text-base lg:text-lg group-hover:scale-110 transition-transform duration-300"
                          aria-hidden="true"
                        />
                      );
                    })()}
                  </div>
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div
                            className="font-medium text-center text-xs sm:text-sm lg:text-base leading-tight w-full"
                            style={{
                              display: "-webkit-box",
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: "vertical",
                              overflow: "hidden",
                              wordBreak: "break-word",
                              hyphens: "auto",
                              whiteSpace: "normal",
                            }}
                            title={
                              link.text.length > 30 ? link.text : undefined
                            }
                          >
                            {link.text}
                          </div>
                        </TooltipTrigger>
                        {link.text.length > 30 && (
                          <TooltipContent>
                            <p className="max-w-xs text-center break-words">
                              {link.text}
                            </p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
              </div>

              {/* Enhanced ripple effect */}
              <div
                className={cn(
                  "absolute inset-0 bg-white/20 scale-0 transition-transform duration-200 rounded-2xl",
                  "group-active:scale-100 group-active:duration-75",
                  isClicked && "scale-100 duration-75"
                )}
              ></div>
            </Button>

            {/* Hidden description for screen readers */}
            <span id={`link-description-${index}`} className="sr-only">
              {linkType === "external" && "Link externo que abre em nova aba"}
              {linkType === "social" && "Link para rede social"}
              {linkType === "location" && "Link para localização no mapa"}
              {linkType === "email" && "Link para enviar email"}
              {linkType === "phone" && "Link para fazer ligação"}
              {linkType === "anchor" && "Link interno da página"}
            </span>
          </div>
        );
      })}
    </div>
  );
};

export default LinksSection;
