{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatUrl(url: string): string {\n  if (!url) return \"\"\n  \n  // Add https:// if no protocol is specified\n  if (!url.startsWith(\"http://\") && !url.startsWith(\"https://\")) {\n    return `https://${url}`\n  }\n  \n  return url\n}\n\nexport function validateUsername(username: string): boolean {\n  // Username validation: alphanumeric, underscores, hyphens, 3-30 characters\n  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/\n  return usernameRegex.test(username)\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.substring(0, maxLength) + \"...\"\n}\n\nexport function generateMetaTitle(username: string, displayName?: string): string {\n  const name = displayName || username\n  return `${name} | AvencaLink`\n}\n\nexport function generateMetaDescription(bio?: string, username?: string): string {\n  if (bio) {\n    return truncateText(bio, 160)\n  }\n  return `Check out ${username}'s links on AvencaLink`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,CAAC,IAAI,UAAU,CAAC,aAAa;QAC7D,OAAO,CAAC,QAAQ,EAAE,KAAK;IACzB;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,2EAA2E;IAC3E,MAAM,gBAAgB;IACtB,OAAO,cAAc,IAAI,CAAC;AAC5B;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC;AAEO,SAAS,kBAAkB,QAAgB,EAAE,WAAoB;IACtE,MAAM,OAAO,eAAe;IAC5B,OAAO,GAAG,KAAK,aAAa,CAAC;AAC/B;AAEO,SAAS,wBAAwB,GAAY,EAAE,QAAiB;IACrE,IAAI,KAAK;QACP,OAAO,aAAa,KAAK;IAC3B;IACA,OAAO,CAAC,UAAU,EAAE,SAAS,sBAAsB,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Moon, Sun } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ThemeToggleProps {\r\n  className?: string;\r\n}\r\n\r\nexport function ThemeToggle({ className }: ThemeToggleProps) {\r\n  const { setTheme, resolvedTheme } = useTheme();\r\n  const [mounted, setMounted] = React.useState(false);\r\n\r\n  React.useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    // Render a static placeholder to prevent layout shift and hydration errors\r\n    return <div className={cn(\"h-8 w-16 rounded-full\", className)} />;\r\n  }\r\n\r\n  const isDark = resolvedTheme === \"dark\";\r\n\r\n  // const spring = {\r\n  //   type: \"spring\",\r\n  //   stiffness: 700,\r\n  //   damping: 30,\r\n  // };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex h-8 w-16 cursor-pointer items-center rounded-full p-1\",\r\n        isDark\r\n          ? \"justify-start border border-zinc-800 bg-zinc-950\"\r\n          : \"justify-end border border-zinc-200 bg-gray-50\",\r\n        className\r\n      )}\r\n      onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\r\n      role=\"button\"\r\n      tabIndex={0}\r\n    >\r\n      <span className=\"sr-only\">Toggle theme</span>\r\n      <motion.div\r\n        className={cn(\r\n          \"flex h-6 w-6 items-center justify-center rounded-full\",\r\n          isDark ? \"bg-zinc-800\" : \"bg-gray-200\"\r\n        )}\r\n        layout\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        {isDark ? (\r\n          <Moon className=\"h-4 w-4 text-white\" strokeWidth={1.5} />\r\n        ) : (\r\n          <Sun className=\"h-4 w-4 text-gray-700\" strokeWidth={1.5} />\r\n        )}\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAoB;;IACzD,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,2EAA2E;QAC3E,qBAAO,4TAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;;;;;;IACrD;IAEA,MAAM,SAAS,kBAAkB;IAEjC,mBAAmB;IACnB,oBAAoB;IACpB,oBAAoB;IACpB,iBAAiB;IACjB,KAAK;IAEL,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA,SACI,qDACA,iDACJ;QAEF,SAAS,IAAM,SAAS,SAAS,UAAU;QAC3C,MAAK;QACL,UAAU;;0BAEV,4TAAC;gBAAK,WAAU;0BAAU;;;;;;0BAC1B,4TAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA,SAAS,gBAAgB;gBAE3B,MAAM;gBACN,YAAY;oBAAE,UAAU;gBAAI;0BAE3B,uBACC,4TAAC,yRAAA,CAAA,OAAI;oBAAC,WAAU;oBAAqB,aAAa;;;;;yCAElD,4TAAC,uRAAA,CAAA,MAAG;oBAAC,WAAU;oBAAwB,aAAa;;;;;;;;;;;;;;;;;AAK9D;GAnDgB;;QACsB,4PAAA,CAAA,WAAQ;;;KAD9B", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/types/constants.ts"], "sourcesContent": ["// ============================================================================\n// CONSTANTS AND ENUMS\n// ============================================================================\n\n/**\n * Common icon classes used throughout the application\n */\nexport const ICON_CLASSES = {\n  // Social Media\n  WHATSAPP: 'fa fa-whatsapp',\n  INSTAGRAM: 'fa fa-instagram',\n  FACEBOOK: 'fa fa-facebook',\n  YOUTUBE: 'fa fa-youtube',\n  TIKTOK: 'fa fa-video',\n  PINTEREST: 'fa fa-pinterest',\n  TWITTER: 'fa fa-twitter',\n  LINKEDIN: 'fa fa-linkedin',\n\n  // Actions\n  PHONE: 'fa fa-phone',\n  EMAIL: 'fa fa-envelope',\n  WEBSITE: 'fa fa-globe',\n  LOCATION: 'fa fa-map-marker',\n\n  // Content\n  BOOK: 'fa fa-book',\n  FILE: 'fa fa-file-text',\n  FOLDER: 'fa fa-folder-open',\n  FORM: 'fa fa-wpforms',\n  EYE: 'fa fa-eye',\n  INFO: 'fa fa-info-circle',\n  USERS: 'fa fa-users',\n\n  // Placeholder\n  PLACEHOLDER: '#',\n} as const\n\n/**\n * Default color schemes\n */\nexport const DEFAULT_COLOR_SCHEMES = {\n  DARK: {\n    background: '#1f1f1f',\n    linkText: '#ffffff',\n    primary: '#393939',\n    secondary: '#828282',\n    socialIconBackground: '#393939',\n  },\n  LIGHT: {\n    background: '#d7d6d6',\n    linkText: '#ffffff',\n    primary: '#393939',\n    secondary: '#828282',\n    socialIconBackground: '#393939',\n  },\n  WARM: {\n    background: '#d7d5d3',\n    linkText: '#ffffff',\n    primary: '#201f1f',\n    secondary: '#c7b29d',\n    socialIconBackground: '#201f1f',\n  },\n  NEUTRAL: {\n    background: '#d8d8d8',\n    linkText: '#d8d8d8',\n    primary: '#1f1f1f',\n    secondary: '#727272',\n    socialIconBackground: '#979797',\n  },\n} as const\n\n/**\n * Rating values for reviews\n */\nexport const RATING_VALUES = [1, 2, 3, 4, 5] as const\n\n/**\n * Section types\n */\nexport const SECTION_TYPES = {\n  FEATURES: 'featuresSection',\n  SERVICES: 'servicesSection',\n  GENERIC: 'genericSection',\n  GALLERY: 'gallery',\n  REVIEWS: 'reviews',\n  VIDEO: 'video',\n} as const\n\n/**\n * Button types\n */\nexport const BUTTON_TYPES = {\n  PRIMARY: 'primary',\n  SECONDARY: 'secondary',\n} as const\n\n/**\n * Default button configurations for different section types\n */\nexport const SECTION_BUTTON_CONFIG = {\n  features: {\n    primaryButtonText: 'Ver',\n    secondaryButtonText: 'Contato',\n    showBadge: false,\n  },\n  services: {\n    primaryButtonText: 'Solicitar',\n    secondaryButtonText: 'Info',\n    showBadge: true,\n  },\n  generic: {\n    primaryButtonText: 'Ver Mais',\n    secondaryButtonText: 'Contato',\n    showBadge: false,\n  },\n} as const\n\n/**\n * Common URL patterns for validation\n */\nexport const URL_PATTERNS = {\n  WHATSAPP: /^https:\\/\\/wa\\.me\\//,\n  INSTAGRAM: /^https:\\/\\/(www\\.)?instagram\\.com\\//,\n  FACEBOOK: /^https:\\/\\/(www\\.)?facebook\\.com\\//,\n  YOUTUBE: /^https:\\/\\/(www\\.)?youtube\\.com\\//,\n  TIKTOK: /^https:\\/\\/(www\\.)?tiktok\\.com\\//,\n  PINTEREST: /^https:\\/\\/(www\\.)?pinterest\\.com\\//,\n  GOOGLE_DRIVE: /^https:\\/\\/drive\\.google\\.com\\//,\n  GOOGLE_DOCS: /^https:\\/\\/docs\\.google\\.com\\//,\n  GOOGLE_FORMS: /^https:\\/\\/forms\\.gle\\//,\n  GOOGLE_MAPS: /^https:\\/\\/maps\\.app\\.goo\\.gl\\//,\n} as const\n\n/**\n * Image placeholder URLs\n */\nexport const PLACEHOLDER_IMAGES = {\n  USER_AVATAR: 'https://db.avenca.cloud/images/2025/01/20/user_placeholder.png',\n  GALLERY_IMAGE: 'https://images.unsplash.com/photo-1660505102581-85cffa4e6550',\n  RANDOM_USER_FEMALE: 'https://randomuser.me/api/portraits/women/',\n  RANDOM_USER_MALE: 'https://randomuser.me/api/portraits/men/',\n} as const\n\n/**\n * Default values for new sections\n */\nexport const DEFAULT_SECTION_VALUES = {\n  ENABLED: true,\n  DISABLED: false,\n  EMPTY_DESCRIPTION: '',\n  EMPTY_TITLE: '',\n  EMPTY_URL: '#',\n  PLACEHOLDER_ICON: '#',\n} as const\n\n/**\n * Type definitions for constants\n */\nexport type IconClass = typeof ICON_CLASSES[keyof typeof ICON_CLASSES]\nexport type ColorScheme = typeof DEFAULT_COLOR_SCHEMES[keyof typeof DEFAULT_COLOR_SCHEMES]\nexport type RatingValue = typeof RATING_VALUES[number]\nexport type SectionType = typeof SECTION_TYPES[keyof typeof SECTION_TYPES]\nexport type ButtonType = typeof BUTTON_TYPES[keyof typeof BUTTON_TYPES]\nexport type SectionButtonConfig = typeof SECTION_BUTTON_CONFIG[keyof typeof SECTION_BUTTON_CONFIG]\n"], "names": [], "mappings": "AAAA,+EAA+E;AAC/E,sBAAsB;AACtB,+EAA+E;AAE/E;;CAEC;;;;;;;;;;;AACM,MAAM,eAAe;IAC1B,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,SAAS;IACT,QAAQ;IACR,WAAW;IACX,SAAS;IACT,UAAU;IAEV,UAAU;IACV,OAAO;IACP,OAAO;IACP,SAAS;IACT,UAAU;IAEV,UAAU;IACV,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,KAAK;IACL,MAAM;IACN,OAAO;IAEP,cAAc;IACd,aAAa;AACf;AAKO,MAAM,wBAAwB;IACnC,MAAM;QACJ,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;QACX,sBAAsB;IACxB;IACA,OAAO;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;QACX,sBAAsB;IACxB;IACA,MAAM;QACJ,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;QACX,sBAAsB;IACxB;IACA,SAAS;QACP,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;QACX,sBAAsB;IACxB;AACF;AAKO,MAAM,gBAAgB;IAAC;IAAG;IAAG;IAAG;IAAG;CAAE;AAKrC,MAAM,gBAAgB;IAC3B,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAKO,MAAM,eAAe;IAC1B,SAAS;IACT,WAAW;AACb;AAKO,MAAM,wBAAwB;IACnC,UAAU;QACR,mBAAmB;QACnB,qBAAqB;QACrB,WAAW;IACb;IACA,UAAU;QACR,mBAAmB;QACnB,qBAAqB;QACrB,WAAW;IACb;IACA,SAAS;QACP,mBAAmB;QACnB,qBAAqB;QACrB,WAAW;IACb;AACF;AAKO,MAAM,eAAe;IAC1B,UAAU;IACV,WAAW;IACX,UAAU;IACV,SAAS;IACT,QAAQ;IACR,WAAW;IACX,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;AACf;AAKO,MAAM,qBAAqB;IAChC,aAAa;IACb,eAAe;IACf,oBAAoB;IACpB,kBAAkB;AACpB;AAKO,MAAM,yBAAyB;IACpC,SAAS;IACT,UAAU;IACV,mBAAmB;IACnB,aAAa;IACb,WAAW;IACX,kBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/buttonUtils.ts"], "sourcesContent": ["import { ButtonConfig } from '@/types/user'\r\nimport { SECTION_BUTTON_CONFIG } from '@/types/constants'\r\n\r\n/**\r\n * Gets button configuration for a section, prioritizing API data over defaults\r\n */\r\nexport function getButtonConfig(\r\n    sectionType: 'features' | 'services' | 'generic',\r\n    apiButtonConfig?: ButtonConfig\r\n): Required<ButtonConfig> {\r\n    const defaultConfig = SECTION_BUTTON_CONFIG[sectionType]\r\n\r\n    return {\r\n        primaryButtonText: apiButtonConfig?.primaryButtonText ?? defaultConfig.primaryButtonText,\r\n        secondaryButtonText: apiButtonConfig?.secondaryButtonText ?? defaultConfig.secondaryButtonText,\r\n        showBadge: apiButtonConfig?.showBadge ?? defaultConfig.showBadge,\r\n    }\r\n}\r\n\r\n/**\r\n * Validates button configuration\r\n */\r\nexport function validateButtonConfig(config: ButtonConfig): {\r\n    isValid: boolean\r\n    errors: string[]\r\n} {\r\n    const errors: string[] = []\r\n\r\n    if (config.primaryButtonText !== undefined && typeof config.primaryButtonText !== 'string') {\r\n        errors.push('primaryButtonText must be a string')\r\n    }\r\n\r\n    if (config.secondaryButtonText !== undefined && typeof config.secondaryButtonText !== 'string') {\r\n        errors.push('secondaryButtonText must be a string')\r\n    }\r\n\r\n    if (config.showBadge !== undefined && typeof config.showBadge !== 'boolean') {\r\n        errors.push('showBadge must be a boolean')\r\n    }\r\n\r\n    return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n    }\r\n}\r\n\r\n/**\r\n * Creates default button configuration for a section type\r\n */\r\nexport function createDefaultButtonConfig(\r\n    sectionType: 'features' | 'services' | 'generic'\r\n): Required<ButtonConfig> {\r\n    return { ...SECTION_BUTTON_CONFIG[sectionType] }\r\n}\r\n\r\n/**\r\n * Merges multiple button configurations with priority order\r\n */\r\nexport function mergeButtonConfigs(\r\n    ...configs: (ButtonConfig | undefined)[]\r\n): ButtonConfig {\r\n    const result: ButtonConfig = {}\r\n\r\n    for (const config of configs) {\r\n        if (config) {\r\n            if (config.primaryButtonText !== undefined) {\r\n                result.primaryButtonText = config.primaryButtonText\r\n            }\r\n            if (config.secondaryButtonText !== undefined) {\r\n                result.secondaryButtonText = config.secondaryButtonText\r\n            }\r\n            if (config.showBadge !== undefined) {\r\n                result.showBadge = config.showBadge\r\n            }\r\n        }\r\n    }\r\n\r\n    return result\r\n}"], "names": [], "mappings": ";;;;;;AACA;;AAKO,SAAS,gBACZ,WAAgD,EAChD,eAA8B;IAE9B,MAAM,gBAAgB,qHAAA,CAAA,wBAAqB,CAAC,YAAY;IAExD,OAAO;QACH,mBAAmB,iBAAiB,qBAAqB,cAAc,iBAAiB;QACxF,qBAAqB,iBAAiB,uBAAuB,cAAc,mBAAmB;QAC9F,WAAW,iBAAiB,aAAa,cAAc,SAAS;IACpE;AACJ;AAKO,SAAS,qBAAqB,MAAoB;IAIrD,MAAM,SAAmB,EAAE;IAE3B,IAAI,OAAO,iBAAiB,KAAK,aAAa,OAAO,OAAO,iBAAiB,KAAK,UAAU;QACxF,OAAO,IAAI,CAAC;IAChB;IAEA,IAAI,OAAO,mBAAmB,KAAK,aAAa,OAAO,OAAO,mBAAmB,KAAK,UAAU;QAC5F,OAAO,IAAI,CAAC;IAChB;IAEA,IAAI,OAAO,SAAS,KAAK,aAAa,OAAO,OAAO,SAAS,KAAK,WAAW;QACzE,OAAO,IAAI,CAAC;IAChB;IAEA,OAAO;QACH,SAAS,OAAO,MAAM,KAAK;QAC3B;IACJ;AACJ;AAKO,SAAS,0BACZ,WAAgD;IAEhD,OAAO;QAAE,GAAG,qHAAA,CAAA,wBAAqB,CAAC,YAAY;IAAC;AACnD;AAKO,SAAS,mBACZ,GAAG,OAAqC;IAExC,MAAM,SAAuB,CAAC;IAE9B,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,QAAQ;YACR,IAAI,OAAO,iBAAiB,KAAK,WAAW;gBACxC,OAAO,iBAAiB,GAAG,OAAO,iBAAiB;YACvD;YACA,IAAI,OAAO,mBAAmB,KAAK,WAAW;gBAC1C,OAAO,mBAAmB,GAAG,OAAO,mBAAmB;YAC3D;YACA,IAAI,OAAO,SAAS,KAAK,WAAW;gBAChC,OAAO,SAAS,GAAG,OAAO,SAAS;YACvC;QACJ;IACJ;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,4TAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4TAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,4TAAC,kRAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/tooltip.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%-2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,4TAAC,gRAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,4TAAC;kBACC,cAAA,4TAAC,gRAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,4TAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,4TAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,4TAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,4TAAC,gRAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/separator.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,4TAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/UserProfile.tsx"], "sourcesContent": ["import { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { MapPin } from \"lucide-react\";\r\n\r\ninterface UserProfileProps {\r\n  user: {\r\n    avatar: string;\r\n    name: string;\r\n    username: string;\r\n    bio: string;\r\n  };\r\n}\r\n\r\nconst UserProfile = ({ user }: UserProfileProps) => {\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n  // Simulate image loading\r\n  useEffect(() => {\r\n    const img = new Image();\r\n    img.onload = () => setIsImageLoaded(true);\r\n    img.src = user.avatar;\r\n  }, [user.avatar]);\r\n\r\n  const specialties = [\"Traços Finos\", \"Realismo\", \"Floral\", \"Minimalista\"];\r\n\r\n  return (\r\n    <TooltipProvider>\r\n      <div className=\"text-center mb-8 animate-fade-in\">\r\n        {/* Enhanced Avatar Section */}\r\n        <div className=\"relative inline-block mb-6 group\">\r\n          <div className=\"relative\">\r\n            {/* Avatar with loading state and enhanced effects */}\r\n            <Avatar className=\"w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 mx-auto shadow-strong ring-4 ring-primary/20 transition-all duration-500 hover:ring-primary/40 hover:ring-8 hover:shadow-2xl group-hover:scale-105\">\r\n              <AvatarImage\r\n                src={user.avatar}\r\n                alt={user.name}\r\n                className={`object-cover transition-all duration-500 ${\r\n                  isImageLoaded ? \"opacity-100 scale-100\" : \"opacity-0 scale-95\"\r\n                } group-hover:scale-110`}\r\n                onLoad={() => setIsImageLoaded(true)}\r\n              />\r\n              <AvatarFallback className=\"bg-gradient-primary text-primary-foreground text-2xl sm:text-3xl lg:text-4xl font-bold animate-pulse\">\r\n                {user.name.charAt(0)}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n\r\n            {/* Enhanced online indicator with tooltip */}\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <div className=\"absolute -bottom-1 -right-1 w-6 h-6 sm:w-7 sm:h-7 bg-green-500 rounded-full border-4 border-background animate-pulse cursor-help shadow-lg\">\r\n                  <div className=\"absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75\"></div>\r\n                </div>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p className=\"text-xs font-medium\">Ativo agora</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Name Section */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex items-center justify-center gap-2 mb-2\">\r\n            <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground tracking-tight bg-gradient-to-r from-primary to-primary/80 bg-clip-text\">\r\n              {user.name}\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Username with copy functionality */}\r\n          <div className=\"flex items-center justify-center gap-2 mb-3\">\r\n            <p className=\"text-sm sm:text-base text-muted-foreground font-medium\">\r\n              @{user.username}\r\n            </p>\r\n            <div className=\"flex items-center gap-1 text-muted-foreground\">\r\n              <MapPin className=\"w-3 h-3\" />\r\n              <span className=\"text-xs\">Palmeira das Missões</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Specialties badges */}\r\n          <div className=\"flex flex-wrap justify-center gap-2 mb-4\">\r\n            {specialties.map((specialty, index) => (\r\n              <Badge\r\n                key={specialty}\r\n                variant=\"secondary\"\r\n                className=\"text-xs px-3 py-1 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors duration-300\"\r\n                style={{ animationDelay: `${index * 0.1}s` }}\r\n              >\r\n                {specialty}\r\n              </Badge>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced Bio Section */}\r\n        <Card className=\"max-w-md sm:max-w-lg lg:max-w-xl mx-auto mb-6 bg-card/20 backdrop-blur-sm border-border/50 shadow-none hover:shadow-xl transition-all duration-300\">\r\n          <CardContent className=\"p-4 sm:p-6\">\r\n            <p className=\"text-sm sm:text-base lg:text-lg text-foreground leading-relaxed sm:leading-loose text-center\">\r\n              {user.bio}\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Enhanced Decorative Elements */}\r\n        <div className=\"flex items-center justify-center gap-4 mb-4\">\r\n          <Separator className=\"flex-1 max-w-16 bg-gradient-to-r from-primary to-primary/80 opacity-60\" />\r\n          <div className=\"flex items-center gap-1\">\r\n            <div className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></div>\r\n            <div\r\n              className=\"w-1.5 h-1.5 bg-primary/70 rounded-full animate-pulse\"\r\n              style={{ animationDelay: \"0.5s\" }}\r\n            ></div>\r\n            <div\r\n              className=\"w-1 h-1 bg-primary/50 rounded-full animate-pulse\"\r\n              style={{ animationDelay: \"1s\" }}\r\n            ></div>\r\n          </div>\r\n          <Separator className=\"flex-1 max-w-16 bg-gradient-to-r from-primary to-primary/80 opacity-60\" />\r\n        </div>\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n};\r\n\r\nexport default UserProfile;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAMA;AACA;AACA;;;;;;;;;;AAWA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAoB;;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,yBAAyB;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,MAAM,IAAI;YAChB,IAAI,MAAM;yCAAG,IAAM,iBAAiB;;YACpC,IAAI,GAAG,GAAG,KAAK,MAAM;QACvB;gCAAG;QAAC,KAAK,MAAM;KAAC;IAEhB,MAAM,cAAc;QAAC;QAAgB;QAAY;QAAU;KAAc;IAEzE,qBACE,4TAAC,+HAAA,CAAA,kBAAe;kBACd,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,8HAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,4TAAC,8HAAA,CAAA,cAAW;wCACV,KAAK,KAAK,MAAM;wCAChB,KAAK,KAAK,IAAI;wCACd,WAAW,CAAC,yCAAyC,EACnD,gBAAgB,0BAA0B,qBAC3C,sBAAsB,CAAC;wCACxB,QAAQ,IAAM,iBAAiB;;;;;;kDAEjC,4TAAC,8HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;0CAKtB,4TAAC,+HAAA,CAAA,UAAO;;kDACN,4TAAC,+HAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;kDAGnB,4TAAC,+HAAA,CAAA,iBAAc;kDACb,cAAA,4TAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3C,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAG,WAAU;0CACX,KAAK,IAAI;;;;;;;;;;;sCAKd,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAE,WAAU;;wCAAyD;wCAClE,KAAK,QAAQ;;;;;;;8CAEjB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,4TAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAK9B,4TAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,WAAW,sBAC3B,4TAAC,6HAAA,CAAA,QAAK;oCAEJ,SAAQ;oCACR,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;8CAE1C;mCALI;;;;;;;;;;;;;;;;8BAYb,4TAAC,4HAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,4TAAC;4BAAE,WAAU;sCACV,KAAK,GAAG;;;;;;;;;;;;;;;;8BAMf,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,iIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;;;;;8CACf,4TAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CAElC,4TAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,gBAAgB;oCAAK;;;;;;;;;;;;sCAGlC,4TAAC,iIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK/B;GA5GM;KAAA;uCA8GS", "debugId": null}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/iconUtils.ts"], "sourcesContent": ["import {\r\n    Facebook,\r\n    Twitter,\r\n    Instagram,\r\n    Linkedin,\r\n    Youtube,\r\n    Github,\r\n    Globe,\r\n    Mail,\r\n    Phone,\r\n    MessageCircle,\r\n    Share2,\r\n    ExternalLink,\r\n} from \"lucide-react\";\r\n\r\n/**\r\n * Maps icon names to their corresponding Lucide React components\r\n * Handles various icon name formats including Font Awesome prefixes\r\n *\r\n * @param iconName - The icon name to map (supports fa-, fab-, fas-, far- prefixes)\r\n * @returns The corresponding React component or ExternalLink as fallback\r\n */\r\nexport const getIconComponent = (iconName: string): React.ComponentType<React.SVGProps<SVGSVGElement>> => {\r\n    const iconMap: Record<\r\n        string,\r\n        React.ComponentType<React.SVGProps<SVGSVGElement>>\r\n    > = {\r\n        facebook: Facebook,\r\n        twitter: Twitter,\r\n        instagram: Instagram,\r\n        linkedin: Linkedin,\r\n        youtube: Youtube,\r\n        github: Github,\r\n        globe: Globe,\r\n        website: Globe,\r\n        mail: Mail,\r\n        email: Mail,\r\n        phone: Phone,\r\n        whatsapp: MessageCircle,\r\n        telegram: MessageCircle,\r\n        share: Share2,\r\n        link: ExternalLink,\r\n        external: ExternalLink,\r\n    };\r\n\r\n    // Clean the icon name by removing Font Awesome prefixes and converting to lowercase\r\n    // Handle formats like \"fa fa-whatsapp\", \"fab fa-instagram\", \"fas fa-phone\", etc.\r\n    const cleanName = iconName\r\n        .replace(\r\n            /^(fa\\s+fa-|fab\\s+fa-|fas\\s+fa-|far\\s+fa-|icon-|fa-|fab-|fas-|far-)/i,\r\n            \"\"\r\n        )\r\n        .toLowerCase()\r\n        .trim();\r\n\r\n    return iconMap[cleanName] || ExternalLink;\r\n};"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAsBO,MAAM,mBAAmB,CAAC;IAC7B,MAAM,UAGF;QACA,UAAU,iSAAA,CAAA,WAAQ;QAClB,SAAS,+RAAA,CAAA,UAAO;QAChB,WAAW,mSAAA,CAAA,YAAS;QACpB,UAAU,iSAAA,CAAA,WAAQ;QAClB,SAAS,+RAAA,CAAA,UAAO;QAChB,QAAQ,6RAAA,CAAA,SAAM;QACd,OAAO,2RAAA,CAAA,QAAK;QACZ,SAAS,2RAAA,CAAA,QAAK;QACd,MAAM,yRAAA,CAAA,OAAI;QACV,OAAO,yRAAA,CAAA,OAAI;QACX,OAAO,2RAAA,CAAA,QAAK;QACZ,UAAU,+SAAA,CAAA,gBAAa;QACvB,UAAU,+SAAA,CAAA,gBAAa;QACvB,OAAO,iSAAA,CAAA,SAAM;QACb,MAAM,6SAAA,CAAA,eAAY;QAClB,UAAU,6SAAA,CAAA,eAAY;IAC1B;IAEA,oFAAoF;IACpF,iFAAiF;IACjF,MAAM,YAAY,SACb,OAAO,CACJ,uEACA,IAEH,WAAW,GACX,IAAI;IAET,OAAO,OAAO,CAAC,UAAU,IAAI,6SAAA,CAAA,eAAY;AAC7C", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/lib/colorUtils.ts"], "sourcesContent": ["import { ColorSettings } from '@/types/user'\nimport { DEFAULT_COLOR_SCHEMES } from '@/types/constants'\n\n/**\n * Validates if a color string is a valid hex color\n */\nexport function isValidHexColor(color: string): boolean {\n  if (!color || typeof color !== 'string') return false\n  \n  // Remove # if present\n  const cleanColor = color.replace('#', '')\n  \n  // Check if it's a valid hex color (3, 6, or 8 characters)\n  const hexRegex = /^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$|^[0-9A-Fa-f]{8}$/\n  return hexRegex.test(cleanColor)\n}\n\n/**\n * Validates if a color string is a valid CSS color (hex, rgb, rgba, hsl, etc.)\n */\nexport function isValidCSSColor(color: string): boolean {\n  if (!color || typeof color !== 'string') return false\n  \n  // Check for hex colors\n  if (color.startsWith('#')) {\n    return isValidHexColor(color)\n  }\n  \n  // Check for rgb/rgba colors\n  if (color.startsWith('rgb')) {\n    const rgbRegex = /^rgba?\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*(?:,\\s*[\\d.]+)?\\s*\\)$/\n    return rgbRegex.test(color)\n  }\n  \n  // Check for hsl/hsla colors\n  if (color.startsWith('hsl')) {\n    const hslRegex = /^hsla?\\(\\s*\\d+\\s*,\\s*\\d+%\\s*,\\s*\\d+%\\s*(?:,\\s*[\\d.]+)?\\s*\\)$/\n    return hslRegex.test(color)\n  }\n  \n  // Check for named colors (basic validation)\n  const namedColors = [\n    'transparent', 'black', 'white', 'red', 'green', 'blue', 'yellow', \n    'orange', 'purple', 'pink', 'gray', 'grey', 'brown', 'cyan', 'magenta'\n  ]\n  return namedColors.includes(color.toLowerCase())\n}\n\n/**\n * Sanitizes and validates color settings, providing fallbacks for invalid colors\n */\nexport function sanitizeColorSettings(colors: Partial<ColorSettings> | undefined): ColorSettings {\n  const defaultColors = DEFAULT_COLOR_SCHEMES.DARK\n  \n  if (!colors) {\n    return defaultColors\n  }\n  \n  return {\n    background: isValidCSSColor(colors.background || '') \n      ? colors.background! \n      : defaultColors.background,\n    linkText: isValidCSSColor(colors.linkText || '') \n      ? colors.linkText! \n      : defaultColors.linkText,\n    primary: isValidCSSColor(colors.primary || '') \n      ? colors.primary! \n      : defaultColors.primary,\n    secondary: isValidCSSColor(colors.secondary || '') \n      ? colors.secondary! \n      : defaultColors.secondary,\n    socialIconBackground: isValidCSSColor(colors.socialIconBackground || '') \n      ? colors.socialIconBackground! \n      : defaultColors.socialIconBackground,\n  }\n}\n\n/**\n * Creates color variations with opacity for hover and active states\n */\nexport function createColorVariations(baseColor: string) {\n  if (!isValidHexColor(baseColor)) {\n    return {\n      base: baseColor,\n      hover: baseColor,\n      active: baseColor,\n      disabled: baseColor,\n    }\n  }\n  \n  // Remove # if present\n  const cleanColor = baseColor.replace('#', '')\n  \n  // Convert 3-digit hex to 6-digit\n  const fullHex = cleanColor.length === 3 \n    ? cleanColor.split('').map(char => char + char).join('')\n    : cleanColor\n  \n  return {\n    base: `#${fullHex}`,\n    hover: `#${fullHex}E6`, // 90% opacity\n    active: `#${fullHex}CC`, // 80% opacity\n    disabled: `#${fullHex}66`, // 40% opacity\n    light: `#${fullHex}33`, // 20% opacity\n    extraLight: `#${fullHex}1A`, // 10% opacity\n  }\n}\n\n/**\n * Gets the appropriate text color (black or white) based on background color\n */\nexport function getContrastTextColor(backgroundColor: string): string {\n  if (!isValidHexColor(backgroundColor)) {\n    return '#ffffff' // Default to white for invalid colors\n  }\n  \n  // Remove # if present\n  const cleanColor = backgroundColor.replace('#', '')\n  \n  // Convert 3-digit hex to 6-digit\n  const fullHex = cleanColor.length === 3 \n    ? cleanColor.split('').map(char => char + char).join('')\n    : cleanColor\n  \n  // Convert hex to RGB\n  const r = parseInt(fullHex.substr(0, 2), 16)\n  const g = parseInt(fullHex.substr(2, 2), 16)\n  const b = parseInt(fullHex.substr(4, 2), 16)\n  \n  // Calculate luminance\n  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255\n  \n  // Return black for light backgrounds, white for dark backgrounds\n  return luminance > 0.5 ? '#000000' : '#ffffff'\n}\n\n/**\n * Creates a complete color theme with all necessary variations\n */\nexport function createColorTheme(colors: Partial<ColorSettings> | undefined) {\n  const sanitizedColors = sanitizeColorSettings(colors)\n  \n  return {\n    background: sanitizedColors.background,\n    foreground: getContrastTextColor(sanitizedColors.background),\n    primary: {\n      ...createColorVariations(sanitizedColors.primary),\n      foreground: getContrastTextColor(sanitizedColors.primary),\n    },\n    secondary: {\n      ...createColorVariations(sanitizedColors.secondary),\n      foreground: getContrastTextColor(sanitizedColors.secondary),\n    },\n    socialIcon: {\n      ...createColorVariations(sanitizedColors.socialIconBackground),\n      foreground: getContrastTextColor(sanitizedColors.socialIconBackground),\n    },\n    linkText: sanitizedColors.linkText,\n  }\n}\n\n/**\n * Applies color theme to CSS custom properties\n */\nexport function applyColorTheme(colors: Partial<ColorSettings> | undefined): React.CSSProperties {\n  const theme = createColorTheme(colors)\n  \n  return {\n    '--color-background': theme.background,\n    '--color-foreground': theme.foreground,\n    '--color-primary': theme.primary.base,\n    '--color-primary-hover': theme.primary.hover,\n    '--color-primary-active': theme.primary.active,\n    '--color-primary-foreground': theme.primary.foreground,\n    '--color-secondary': theme.secondary.base,\n    '--color-secondary-hover': theme.secondary.hover,\n    '--color-secondary-active': theme.secondary.active,\n    '--color-secondary-foreground': theme.secondary.foreground,\n    '--color-social-icon': theme.socialIcon.base,\n    '--color-social-icon-hover': theme.socialIcon.hover,\n    '--color-social-icon-foreground': theme.socialIcon.foreground,\n    '--color-link-text': theme.linkText,\n  } as React.CSSProperties\n}\n"], "names": [], "mappings": ";;;;;;;;;AACA;;AAKO,SAAS,gBAAgB,KAAa;IAC3C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,sBAAsB;IACtB,MAAM,aAAa,MAAM,OAAO,CAAC,KAAK;IAEtC,0DAA0D;IAC1D,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC;AACvB;AAKO,SAAS,gBAAgB,KAAa;IAC3C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,uBAAuB;IACvB,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,OAAO,gBAAgB;IACzB;IAEA,4BAA4B;IAC5B,IAAI,MAAM,UAAU,CAAC,QAAQ;QAC3B,MAAM,WAAW;QACjB,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,4BAA4B;IAC5B,IAAI,MAAM,UAAU,CAAC,QAAQ;QAC3B,MAAM,WAAW;QACjB,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,4CAA4C;IAC5C,MAAM,cAAc;QAClB;QAAe;QAAS;QAAS;QAAO;QAAS;QAAQ;QACzD;QAAU;QAAU;QAAQ;QAAQ;QAAQ;QAAS;QAAQ;KAC9D;IACD,OAAO,YAAY,QAAQ,CAAC,MAAM,WAAW;AAC/C;AAKO,SAAS,sBAAsB,MAA0C;IAC9E,MAAM,gBAAgB,qHAAA,CAAA,wBAAqB,CAAC,IAAI;IAEhD,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,OAAO;QACL,YAAY,gBAAgB,OAAO,UAAU,IAAI,MAC7C,OAAO,UAAU,GACjB,cAAc,UAAU;QAC5B,UAAU,gBAAgB,OAAO,QAAQ,IAAI,MACzC,OAAO,QAAQ,GACf,cAAc,QAAQ;QAC1B,SAAS,gBAAgB,OAAO,OAAO,IAAI,MACvC,OAAO,OAAO,GACd,cAAc,OAAO;QACzB,WAAW,gBAAgB,OAAO,SAAS,IAAI,MAC3C,OAAO,SAAS,GAChB,cAAc,SAAS;QAC3B,sBAAsB,gBAAgB,OAAO,oBAAoB,IAAI,MACjE,OAAO,oBAAoB,GAC3B,cAAc,oBAAoB;IACxC;AACF;AAKO,SAAS,sBAAsB,SAAiB;IACrD,IAAI,CAAC,gBAAgB,YAAY;QAC/B,OAAO;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa,UAAU,OAAO,CAAC,KAAK;IAE1C,iCAAiC;IACjC,MAAM,UAAU,WAAW,MAAM,KAAK,IAClC,WAAW,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA,OAAQ,OAAO,MAAM,IAAI,CAAC,MACnD;IAEJ,OAAO;QACL,MAAM,CAAC,CAAC,EAAE,SAAS;QACnB,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;QACtB,QAAQ,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;QACvB,UAAU,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;QACzB,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;QACtB,YAAY,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC;IAC7B;AACF;AAKO,SAAS,qBAAqB,eAAuB;IAC1D,IAAI,CAAC,gBAAgB,kBAAkB;QACrC,OAAO,UAAU,sCAAsC;;IACzD;IAEA,sBAAsB;IACtB,MAAM,aAAa,gBAAgB,OAAO,CAAC,KAAK;IAEhD,iCAAiC;IACjC,MAAM,UAAU,WAAW,MAAM,KAAK,IAClC,WAAW,KAAK,CAAC,IAAI,GAAG,CAAC,CAAA,OAAQ,OAAO,MAAM,IAAI,CAAC,MACnD;IAEJ,qBAAqB;IACrB,MAAM,IAAI,SAAS,QAAQ,MAAM,CAAC,GAAG,IAAI;IACzC,MAAM,IAAI,SAAS,QAAQ,MAAM,CAAC,GAAG,IAAI;IACzC,MAAM,IAAI,SAAS,QAAQ,MAAM,CAAC,GAAG,IAAI;IAEzC,sBAAsB;IACtB,MAAM,YAAY,CAAC,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI;IAExD,iEAAiE;IACjE,OAAO,YAAY,MAAM,YAAY;AACvC;AAKO,SAAS,iBAAiB,MAA0C;IACzE,MAAM,kBAAkB,sBAAsB;IAE9C,OAAO;QACL,YAAY,gBAAgB,UAAU;QACtC,YAAY,qBAAqB,gBAAgB,UAAU;QAC3D,SAAS;YACP,GAAG,sBAAsB,gBAAgB,OAAO,CAAC;YACjD,YAAY,qBAAqB,gBAAgB,OAAO;QAC1D;QACA,WAAW;YACT,GAAG,sBAAsB,gBAAgB,SAAS,CAAC;YACnD,YAAY,qBAAqB,gBAAgB,SAAS;QAC5D;QACA,YAAY;YACV,GAAG,sBAAsB,gBAAgB,oBAAoB,CAAC;YAC9D,YAAY,qBAAqB,gBAAgB,oBAAoB;QACvE;QACA,UAAU,gBAAgB,QAAQ;IACpC;AACF;AAKO,SAAS,gBAAgB,MAA0C;IACxE,MAAM,QAAQ,iBAAiB;IAE/B,OAAO;QACL,sBAAsB,MAAM,UAAU;QACtC,sBAAsB,MAAM,UAAU;QACtC,mBAAmB,MAAM,OAAO,CAAC,IAAI;QACrC,yBAAyB,MAAM,OAAO,CAAC,KAAK;QAC5C,0BAA0B,MAAM,OAAO,CAAC,MAAM;QAC9C,8BAA8B,MAAM,OAAO,CAAC,UAAU;QACtD,qBAAqB,MAAM,SAAS,CAAC,IAAI;QACzC,2BAA2B,MAAM,SAAS,CAAC,KAAK;QAChD,4BAA4B,MAAM,SAAS,CAAC,MAAM;QAClD,gCAAgC,MAAM,SAAS,CAAC,UAAU;QAC1D,uBAAuB,MAAM,UAAU,CAAC,IAAI;QAC5C,6BAA6B,MAAM,UAAU,CAAC,KAAK;QACnD,kCAAkC,MAAM,UAAU,CAAC,UAAU;QAC7D,qBAAqB,MAAM,QAAQ;IACrC;AACF", "debugId": null}}, {"offset": {"line": 1323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/SocialMedia.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { getIconComponent } from \"@/lib/iconUtils\";\r\nimport { createColorVariations, isValidCSSColor } from \"@/lib/colorUtils\";\r\nimport { memo, useCallback, useState } from \"react\";\r\n\r\ninterface SocialMediaItem {\r\n  classIcon: string;\r\n  text: string;\r\n  url: string;\r\n}\r\n\r\ninterface SocialMediaProps {\r\n  socialMedia: SocialMediaItem[];\r\n  className?: string;\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  variant?: \"default\" | \"minimal\" | \"outlined\";\r\n  colors?: {\r\n    background?: string;\r\n    linkText?: string;\r\n    primary?: string;\r\n    secondary?: string;\r\n    socialIconBackground?: string;\r\n  };\r\n}\r\n\r\nconst SocialMedia = memo(\r\n  ({\r\n    socialMedia,\r\n    className,\r\n    size = \"lg\",\r\n    variant = \"default\",\r\n    colors,\r\n  }: SocialMediaProps) => {\r\n    const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(\r\n      {}\r\n    );\r\n\r\n    const handleSocialClick = useCallback(\r\n      async (url: string, index: number) => {\r\n        if (!url || url === \"#\") return;\r\n\r\n        // Set loading state\r\n        setLoadingStates((prev) => ({ ...prev, [index]: true }));\r\n\r\n        try {\r\n          // Add a small delay for better UX feedback\r\n          await new Promise((resolve) => setTimeout(resolve, 200));\r\n          window.open(url, \"_blank\", \"noopener,noreferrer\");\r\n        } catch (error) {\r\n          console.error(\"Error opening social media link:\", error);\r\n        } finally {\r\n          // Clear loading state\r\n          setTimeout(() => {\r\n            setLoadingStates((prev) => ({ ...prev, [index]: false }));\r\n          }, 300);\r\n        }\r\n      },\r\n      []\r\n    );\r\n\r\n    const handleKeyDown = useCallback(\r\n      (event: React.KeyboardEvent, url: string, index: number) => {\r\n        if (event.key === \"Enter\" || event.key === \" \") {\r\n          event.preventDefault();\r\n          handleSocialClick(url, index);\r\n        }\r\n      },\r\n      [handleSocialClick]\r\n    );\r\n\r\n    const getSizeClasses = () => {\r\n      switch (size) {\r\n        case \"sm\":\r\n          return \"w-10 h-10\";\r\n        case \"md\":\r\n          return \"w-12 h-12\";\r\n        case \"lg\":\r\n        default:\r\n          return \"w-16 h-16 sm:w-14 sm:h-14\";\r\n      }\r\n    };\r\n\r\n    const getIconSizeClasses = () => {\r\n      switch (size) {\r\n        case \"sm\":\r\n          return \"text-base\";\r\n        case \"md\":\r\n          return \"text-lg\";\r\n        case \"lg\":\r\n        default:\r\n          return \"text-xl sm:text-lg\";\r\n      }\r\n    };\r\n\r\n    const getVariantClasses = () => {\r\n      switch (variant) {\r\n        case \"minimal\":\r\n          return \"bg-background/80 backdrop-blur-sm border border-border/50 hover:bg-background hover:border-border text-foreground hover:text-primary\";\r\n        case \"outlined\":\r\n          return \"bg-transparent border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 text-primary\";\r\n        case \"default\":\r\n        default:\r\n          return \"bg-gradient-to-r from-primary to-primary/70 hover:bg-gradient-to-r hover:from-primary/90 hover:to-primary/60 text-primary-foreground\";\r\n      }\r\n    };\r\n\r\n    if (!socialMedia?.length) {\r\n      return null;\r\n    }\r\n\r\n    return (\r\n      <div\r\n        className={cn(\r\n          \"flex justify-center items-center flex-wrap gap-3 sm:gap-4 mb-8\",\r\n          \"animate-fade-in\",\r\n          className\r\n        )}\r\n        role=\"navigation\"\r\n        aria-label=\"Social media links\"\r\n      >\r\n        {socialMedia.map((social, index) => {\r\n          const isLoading = loadingStates[index];\r\n          const isDisabled = !social.url || social.url === \"#\";\r\n\r\n          // Create dynamic styles using API colors with validation\r\n          const socialButtonStyle =\r\n            colors?.socialIconBackground &&\r\n            isValidCSSColor(colors.socialIconBackground)\r\n              ? (() => {\r\n                  const colorVariations = createColorVariations(\r\n                    colors.socialIconBackground\r\n                  );\r\n                  return {\r\n                    backgroundColor: colorVariations.base,\r\n                    color: colors.linkText || \"#ffffff\",\r\n                    \"--hover-bg\": colorVariations.hover,\r\n                  } as React.CSSProperties;\r\n                })()\r\n              : {};\r\n\r\n          return (\r\n            <Button\r\n              key={`${social.text}-${index}`}\r\n              onClick={() => handleSocialClick(social.url, index)}\r\n              onKeyDown={(e) => handleKeyDown(e, social.url, index)}\r\n              disabled={isDisabled || isLoading}\r\n              size=\"icon\"\r\n              className={cn(\r\n                getSizeClasses(),\r\n                \"rounded-full p-0 group relative overflow-hidden\",\r\n                \"transform transition-all duration-300 ease-spring\",\r\n                \"shadow-soft hover:shadow-strong\",\r\n                \"focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2\",\r\n                \"hover:scale-110 active:scale-95\",\r\n                \"disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\",\r\n                // Only apply default variant classes if no custom colors\r\n                !colors?.socialIconBackground && getVariantClasses(),\r\n                isLoading && \"animate-pulse\"\r\n              )}\r\n              style={{\r\n                animationDelay: `${index * 0.1}s`,\r\n                animationFillMode: \"both\",\r\n                ...(colors?.socialIconBackground ? socialButtonStyle : {}),\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (\r\n                  colors?.socialIconBackground &&\r\n                  isValidCSSColor(colors.socialIconBackground) &&\r\n                  !isDisabled\r\n                ) {\r\n                  const colorVariations = createColorVariations(\r\n                    colors.socialIconBackground\r\n                  );\r\n                  e.currentTarget.style.backgroundColor = colorVariations.hover;\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (\r\n                  colors?.socialIconBackground &&\r\n                  isValidCSSColor(colors.socialIconBackground) &&\r\n                  !isDisabled\r\n                ) {\r\n                  const colorVariations = createColorVariations(\r\n                    colors.socialIconBackground\r\n                  );\r\n                  e.currentTarget.style.backgroundColor = colorVariations.base;\r\n                }\r\n              }}\r\n              aria-label={`${social.text} - Opens in new tab`}\r\n              title={social.text}\r\n            >\r\n              {/* Loading spinner overlay */}\r\n              {isLoading && (\r\n                <div className=\"absolute inset-0 flex items-center justify-center bg-background/20 backdrop-blur-sm rounded-full z-20\">\r\n                  <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\" />\r\n                </div>\r\n              )}\r\n\r\n              {/* Icon */}\r\n              <span className=\"sr-only\">{social.text}</span>\r\n              {(() => {\r\n                const IconComponent = getIconComponent(social.classIcon);\r\n                return (\r\n                  <IconComponent\r\n                    className={cn(\r\n                      getIconSizeClasses(),\r\n                      \"relative z-10 transition-all duration-300\",\r\n                      \"group-hover:scale-110 group-active:scale-95\",\r\n                      isLoading && \"opacity-50\"\r\n                    )}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                );\r\n              })()}\r\n\r\n              {/* Hover effect overlay */}\r\n              <div\r\n                className={cn(\r\n                  \"absolute inset-0 rounded-full transition-opacity duration-300\",\r\n                  \"opacity-0 group-hover:opacity-100\",\r\n                  variant === \"default\"\r\n                    ? \"bg-gradient-to-r from-white/10 via-white/20 to-white/10\"\r\n                    : \"bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10\"\r\n                )}\r\n              />\r\n\r\n              {/* Ripple effect */}\r\n              <div\r\n                className={cn(\r\n                  \"absolute inset-0 rounded-full transition-transform duration-150\",\r\n                  \"scale-0 group-active:scale-100\",\r\n                  variant === \"default\" ? \"bg-white/20\" : \"bg-primary/20\"\r\n                )}\r\n              />\r\n\r\n              {/* Shine effect on hover */}\r\n              <div className=\"absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500\">\r\n                <div className=\"absolute top-0 left-0 w-full h-full rounded-full bg-linear-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700\" />\r\n              </div>\r\n            </Button>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nSocialMedia.displayName = \"SocialMedia\";\r\n\r\nexport default SocialMedia;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAsBA,MAAM,4BAAc,GAAA,CAAA,GAAA,4RAAA,CAAA,OAAI,AAAD,UACrB,CAAC,EACC,WAAW,EACX,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,MAAM,EACW;;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAGH,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;sDAClC,OAAO,KAAa;YAClB,IAAI,CAAC,OAAO,QAAQ,KAAK;YAEzB,oBAAoB;YACpB;8DAAiB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAK,CAAC;;YAEtD,IAAI;gBACF,2CAA2C;gBAC3C,MAAM,IAAI;kEAAQ,CAAC,UAAY,WAAW,SAAS;;gBACnD,OAAO,IAAI,CAAC,KAAK,UAAU;YAC7B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD,SAAU;gBACR,sBAAsB;gBACtB;kEAAW;wBACT;0EAAiB,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,CAAC,MAAM,EAAE;gCAAM,CAAC;;oBACzD;iEAAG;YACL;QACF;qDACA,EAAE;IAGJ,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAC9B,CAAC,OAA4B,KAAa;YACxC,IAAI,MAAM,GAAG,KAAK,WAAW,MAAM,GAAG,KAAK,KAAK;gBAC9C,MAAM,cAAc;gBACpB,kBAAkB,KAAK;YACzB;QACF;iDACA;QAAC;KAAkB;IAGrB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,aAAa,QAAQ;QACxB,OAAO;IACT;IAEA,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA,mBACA;QAEF,MAAK;QACL,cAAW;kBAEV,YAAY,GAAG,CAAC,CAAC,QAAQ;YACxB,MAAM,YAAY,aAAa,CAAC,MAAM;YACtC,MAAM,aAAa,CAAC,OAAO,GAAG,IAAI,OAAO,GAAG,KAAK;YAEjD,yDAAyD;YACzD,MAAM,oBACJ,QAAQ,wBACR,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,oBAAoB,IACvC,CAAC;gBACC,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAC1C,OAAO,oBAAoB;gBAE7B,OAAO;oBACL,iBAAiB,gBAAgB,IAAI;oBACrC,OAAO,OAAO,QAAQ,IAAI;oBAC1B,cAAc,gBAAgB,KAAK;gBACrC;YACF,CAAC,MACD,CAAC;YAEP,qBACE,4TAAC,8HAAA,CAAA,SAAM;gBAEL,SAAS,IAAM,kBAAkB,OAAO,GAAG,EAAE;gBAC7C,WAAW,CAAC,IAAM,cAAc,GAAG,OAAO,GAAG,EAAE;gBAC/C,UAAU,cAAc;gBACxB,MAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kBACA,mDACA,qDACA,mCACA,+EACA,mCACA,4EACA,yDAAyD;gBACzD,CAAC,QAAQ,wBAAwB,qBACjC,aAAa;gBAEf,OAAO;oBACL,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oBACjC,mBAAmB;oBACnB,GAAI,QAAQ,uBAAuB,oBAAoB,CAAC,CAAC;gBAC3D;gBACA,cAAc,CAAC;oBACb,IACE,QAAQ,wBACR,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,oBAAoB,KAC3C,CAAC,YACD;wBACA,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAC1C,OAAO,oBAAoB;wBAE7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,gBAAgB,KAAK;oBAC/D;gBACF;gBACA,cAAc,CAAC;oBACb,IACE,QAAQ,wBACR,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,oBAAoB,KAC3C,CAAC,YACD;wBACA,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAC1C,OAAO,oBAAoB;wBAE7B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,gBAAgB,IAAI;oBAC9D;gBACF;gBACA,cAAY,GAAG,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAC/C,OAAO,OAAO,IAAI;;oBAGjB,2BACC,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;;;;;;;;;;kCAKnB,4TAAC;wBAAK,WAAU;kCAAW,OAAO,IAAI;;;;;;oBACrC,CAAC;wBACA,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,SAAS;wBACvD,qBACE,4TAAC;4BACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sBACA,6CACA,+CACA,aAAa;4BAEf,eAAY;;;;;;oBAGlB,CAAC;kCAGD,4TAAC;wBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA,qCACA,YAAY,YACR,4DACA;;;;;;kCAKR,4TAAC;wBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mEACA,kCACA,YAAY,YAAY,gBAAgB;;;;;;kCAK5C,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;;;;;;;;;;;eA/FZ,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;QAmGpC;;;;;;AAGN;;AAGF,YAAY,WAAW,GAAG;uCAEX", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/LinksSection.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport { useState, useCallback, useRef, useEffect } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { getIconComponent } from \"@/lib/iconUtils\";\r\nimport { createColorVariations, isValidCSSColor } from \"@/lib/colorUtils\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\ninterface LinkItem {\r\n  classIcon: string;\r\n  text: string;\r\n  url: string;\r\n}\r\n\r\ninterface LinksSectionProps {\r\n  links: LinkItem[];\r\n  isLoading?: boolean;\r\n  colors?: {\r\n    background?: string;\r\n    linkText?: string;\r\n    primary?: string;\r\n    secondary?: string;\r\n    socialIconBackground?: string;\r\n  };\r\n}\r\n\r\n// Enhanced link type detection\r\nconst getLinkType = (\r\n  url: string\r\n): \"external\" | \"email\" | \"phone\" | \"anchor\" | \"social\" | \"location\" => {\r\n  if (url.startsWith(\"mailto:\")) return \"email\";\r\n  if (url.startsWith(\"tel:\")) return \"phone\";\r\n  if (url.startsWith(\"#\")) return \"anchor\";\r\n  if (\r\n    url.includes(\"instagram.com\") ||\r\n    url.includes(\"facebook.com\") ||\r\n    url.includes(\"youtube.com\") ||\r\n    url.includes(\"whatsapp\") ||\r\n    url.includes(\"linktr.ee\")\r\n  )\r\n    return \"social\";\r\n  if (url.includes(\"maps.\") || url.includes(\"goo.gl\")) return \"location\";\r\n  return \"external\";\r\n};\r\n\r\n// Loading skeleton component\r\nconst LinkSkeleton = ({ index }: { index: number }) => (\r\n  <div\r\n    className=\"w-full h-14 sm:h-16 lg:h-18 bg-muted/50 rounded-2xl animate-pulse\"\r\n    style={{\r\n      animationDelay: `${index * 0.1}s`,\r\n      animationDuration: \"1.5s\",\r\n    }}\r\n  >\r\n    <div className=\"flex items-center justify-center space-x-2 sm:space-x-3 h-full px-4 sm:px-6\">\r\n      <div className=\"w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 bg-muted rounded flex-shrink-0\"></div>\r\n      <div className=\"flex-1 h-4 bg-muted rounded max-w-[280px] mx-auto\"></div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nconst LinksSection = ({\r\n  links,\r\n  isLoading = false,\r\n  colors,\r\n}: LinksSectionProps) => {\r\n  const [clickedIndex, setClickedIndex] = useState<number | null>(null);\r\n  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);\r\n  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);\r\n\r\n  // Enhanced link click handler with better error handling\r\n  const handleLinkClick = useCallback(\r\n    async (url: string, index: number, event: React.MouseEvent) => {\r\n      // Prevent double clicks\r\n      if (clickedIndex === index) return;\r\n\r\n      setClickedIndex(index);\r\n\r\n      try {\r\n        if (\r\n          url.startsWith(\"http\") ||\r\n          url.startsWith(\"mailto:\") ||\r\n          url.startsWith(\"tel:\")\r\n        ) {\r\n          // Add analytics tracking if needed\r\n          window.open(url, \"_blank\", \"noopener,noreferrer\");\r\n        } else if (url.startsWith(\"#\")) {\r\n          // Enhanced anchor link handling\r\n          event.preventDefault();\r\n          const element = document.querySelector(url);\r\n          if (element) {\r\n            element.scrollIntoView({\r\n              behavior: \"smooth\",\r\n              block: \"start\",\r\n              inline: \"nearest\",\r\n            });\r\n          } else {\r\n            console.warn(`Anchor element ${url} not found`);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error opening link:\", error);\r\n        // Could show a toast notification here\r\n      } finally {\r\n        // Reset click state after animation\r\n        setTimeout(() => setClickedIndex(null), 300);\r\n      }\r\n    },\r\n    [clickedIndex]\r\n  );\r\n\r\n  // Keyboard navigation\r\n  const handleKeyDown = useCallback(\r\n    (event: React.KeyboardEvent, index: number) => {\r\n      switch (event.key) {\r\n        case \"ArrowDown\": {\r\n          event.preventDefault();\r\n          const nextIndex = Math.min(index + 1, links.length - 1);\r\n          buttonRefs.current[nextIndex]?.focus();\r\n          setFocusedIndex(nextIndex);\r\n          break;\r\n        }\r\n        case \"ArrowUp\": {\r\n          event.preventDefault();\r\n          const prevIndex = Math.max(index - 1, 0);\r\n          buttonRefs.current[prevIndex]?.focus();\r\n          setFocusedIndex(prevIndex);\r\n          break;\r\n        }\r\n        case \"Home\": {\r\n          event.preventDefault();\r\n          buttonRefs.current[0]?.focus();\r\n          setFocusedIndex(0);\r\n          break;\r\n        }\r\n        case \"End\": {\r\n          event.preventDefault();\r\n          const lastIndex = links.length - 1;\r\n          buttonRefs.current[lastIndex]?.focus();\r\n          setFocusedIndex(lastIndex);\r\n          break;\r\n        }\r\n      }\r\n    },\r\n    [links.length]\r\n  );\r\n\r\n  // Initialize button refs\r\n  useEffect(() => {\r\n    buttonRefs.current = buttonRefs.current.slice(0, links.length);\r\n  }, [links.length]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div\r\n        className=\"space-y-3 sm:space-y-4 mb-8\"\r\n        role=\"status\"\r\n        aria-label=\"Carregando links\"\r\n      >\r\n        {Array.from({ length: 6 }).map((_, index) => (\r\n          <LinkSkeleton key={index} index={index} />\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"space-y-3 sm:space-y-4 mb-8\"\r\n      role=\"navigation\"\r\n      aria-label=\"Links principais\"\r\n    >\r\n      {links.map((link, index) => {\r\n        const linkType = getLinkType(link.url);\r\n        const isClicked = clickedIndex === index;\r\n        const isFocused = focusedIndex === index;\r\n\r\n        // Create dynamic styles using API colors with validation\r\n        const buttonStyle =\r\n          colors?.primary && isValidCSSColor(colors.primary)\r\n            ? (() => {\r\n                const colorVariations = createColorVariations(colors.primary);\r\n                return {\r\n                  backgroundColor: colorVariations.base,\r\n                  color: colors.linkText || \"#ffffff\",\r\n                  \"--hover-bg\": colorVariations.hover,\r\n                  \"--active-bg\": colorVariations.active,\r\n                } as React.CSSProperties;\r\n              })()\r\n            : {};\r\n\r\n        return (\r\n          <div\r\n            key={`${link.url}-${index}`}\r\n            className=\"relative group/container\"\r\n          >\r\n            <Button\r\n              ref={(el) => {\r\n                buttonRefs.current[index] = el;\r\n              }}\r\n              onClick={(e) => handleLinkClick(link.url, index, e)}\r\n              onKeyDown={(e) => handleKeyDown(e, index)}\r\n              onFocus={() => setFocusedIndex(index)}\r\n              onBlur={() => setFocusedIndex(null)}\r\n              className={cn(\r\n                \"w-full max-w-full min-h-16 h-auto sm:min-h-18 lg:min-h-20 py-2 sm:py-3 lg:py-4 hover:scale-[1.01] transform transition-all duration-300 ease-out shadow-md hover:shadow-lg font-medium group relative overflow-hidden rounded-2xl\",\r\n                \"focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none\",\r\n                \"active:scale-[0.98] active:shadow-sm\",\r\n                isClicked && \"scale-[0.98] shadow-sm\",\r\n                isFocused && \"ring-2 ring-ring ring-offset-2\",\r\n                // Only apply default gradient if no custom colors\r\n                !colors?.primary &&\r\n                  \"bg-gradient-to-r from-primary to-primary/80 text-primary-foreground\"\r\n              )}\r\n              style={{\r\n                animationDelay: `${index * 0.1}s`,\r\n                animationFillMode: \"both\",\r\n                ...(colors?.primary ? buttonStyle : {}),\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                if (colors?.primary && isValidCSSColor(colors.primary)) {\r\n                  const colorVariations = createColorVariations(colors.primary);\r\n                  e.currentTarget.style.backgroundColor = colorVariations.hover;\r\n                }\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                if (colors?.primary && isValidCSSColor(colors.primary)) {\r\n                  const colorVariations = createColorVariations(colors.primary);\r\n                  e.currentTarget.style.backgroundColor = colorVariations.base;\r\n                }\r\n              }}\r\n              aria-label={`${link.text} - ${\r\n                linkType === \"external\"\r\n                  ? \"Abre em nova aba\"\r\n                  : linkType === \"social\"\r\n                  ? \"Rede social\"\r\n                  : linkType === \"location\"\r\n                  ? \"Localização\"\r\n                  : \"Link interno\"\r\n              }`}\r\n              aria-describedby={`link-description-${index}`}\r\n            >\r\n              {/* Enhanced background gradient overlay */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n\r\n              {/* Shimmer effect on hover */}\r\n              <div className=\"absolute inset-0 -translate-x-full group-hover:translate-x-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-transform duration-700 ease-out\"></div>\r\n\r\n              <div className=\"flex items-center justify-center w-full min-w-0 px-1 sm:px-2 lg:px-4 relative z-10\">\r\n                {/* Main content - Icon and text with proper overflow handling */}\r\n                <div className=\"flex items-center space-x-1 sm:space-x-2 min-w-0 w-full overflow-hidden\">\r\n                  <div className=\"relative flex-shrink-0\">\r\n                    {(() => {\r\n                      const IconComponent = getIconComponent(link.classIcon);\r\n                      return (\r\n                        <IconComponent\r\n                          className=\"text-sm sm:text-base lg:text-lg group-hover:scale-110 transition-transform duration-300\"\r\n                          aria-hidden=\"true\"\r\n                        />\r\n                      );\r\n                    })()}\r\n                  </div>\r\n                  <div className=\"flex-1 min-w-0 overflow-hidden\">\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <div\r\n                            className=\"font-medium text-center text-xs sm:text-sm lg:text-base leading-tight w-full\"\r\n                            style={{\r\n                              display: \"-webkit-box\",\r\n                              WebkitLineClamp: 2,\r\n                              WebkitBoxOrient: \"vertical\",\r\n                              overflow: \"hidden\",\r\n                              wordBreak: \"break-word\",\r\n                              hyphens: \"auto\",\r\n                              whiteSpace: \"normal\",\r\n                            }}\r\n                            title={\r\n                              link.text.length > 30 ? link.text : undefined\r\n                            }\r\n                          >\r\n                            {link.text}\r\n                          </div>\r\n                        </TooltipTrigger>\r\n                        {link.text.length > 30 && (\r\n                          <TooltipContent>\r\n                            <p className=\"max-w-xs text-center break-words\">\r\n                              {link.text}\r\n                            </p>\r\n                          </TooltipContent>\r\n                        )}\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Enhanced ripple effect */}\r\n              <div\r\n                className={cn(\r\n                  \"absolute inset-0 bg-white/20 scale-0 transition-transform duration-200 rounded-2xl\",\r\n                  \"group-active:scale-100 group-active:duration-75\",\r\n                  isClicked && \"scale-100 duration-75\"\r\n                )}\r\n              ></div>\r\n            </Button>\r\n\r\n            {/* Hidden description for screen readers */}\r\n            <span id={`link-description-${index}`} className=\"sr-only\">\r\n              {linkType === \"external\" && \"Link externo que abre em nova aba\"}\r\n              {linkType === \"social\" && \"Link para rede social\"}\r\n              {linkType === \"location\" && \"Link para localização no mapa\"}\r\n              {linkType === \"email\" && \"Link para enviar email\"}\r\n              {linkType === \"phone\" && \"Link para fazer ligação\"}\r\n              {linkType === \"anchor\" && \"Link interno da página\"}\r\n            </span>\r\n          </div>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LinksSection;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAyBA,+BAA+B;AAC/B,MAAM,cAAc,CAClB;IAEA,IAAI,IAAI,UAAU,CAAC,YAAY,OAAO;IACtC,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IACnC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO;IAChC,IACE,IAAI,QAAQ,CAAC,oBACb,IAAI,QAAQ,CAAC,mBACb,IAAI,QAAQ,CAAC,kBACb,IAAI,QAAQ,CAAC,eACb,IAAI,QAAQ,CAAC,cAEb,OAAO;IACT,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW,OAAO;IAC5D,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,eAAe,CAAC,EAAE,KAAK,EAAqB,iBAChD,4TAAC;QACC,WAAU;QACV,OAAO;YACL,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;YACjC,mBAAmB;QACrB;kBAEA,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;;;;;8BACf,4TAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;KAVf;AAeN,MAAM,eAAe,CAAC,EACpB,KAAK,EACL,YAAY,KAAK,EACjB,MAAM,EACY;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAgC,EAAE;IAE1D,yDAAyD;IACzD,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;qDAChC,OAAO,KAAa,OAAe;YACjC,wBAAwB;YACxB,IAAI,iBAAiB,OAAO;YAE5B,gBAAgB;YAEhB,IAAI;gBACF,IACE,IAAI,UAAU,CAAC,WACf,IAAI,UAAU,CAAC,cACf,IAAI,UAAU,CAAC,SACf;oBACA,mCAAmC;oBACnC,OAAO,IAAI,CAAC,KAAK,UAAU;gBAC7B,OAAO,IAAI,IAAI,UAAU,CAAC,MAAM;oBAC9B,gCAAgC;oBAChC,MAAM,cAAc;oBACpB,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,IAAI,SAAS;wBACX,QAAQ,cAAc,CAAC;4BACrB,UAAU;4BACV,OAAO;4BACP,QAAQ;wBACV;oBACF,OAAO;wBACL,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,IAAI,UAAU,CAAC;oBAChD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,uCAAuC;YACzC,SAAU;gBACR,oCAAoC;gBACpC;iEAAW,IAAM,gBAAgB;gEAAO;YAC1C;QACF;oDACA;QAAC;KAAa;IAGhB,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;mDAC9B,CAAC,OAA4B;YAC3B,OAAQ,MAAM,GAAG;gBACf,KAAK;oBAAa;wBAChB,MAAM,cAAc;wBACpB,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,GAAG,MAAM,MAAM,GAAG;wBACrD,WAAW,OAAO,CAAC,UAAU,EAAE;wBAC/B,gBAAgB;wBAChB;oBACF;gBACA,KAAK;oBAAW;wBACd,MAAM,cAAc;wBACpB,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,GAAG;wBACtC,WAAW,OAAO,CAAC,UAAU,EAAE;wBAC/B,gBAAgB;wBAChB;oBACF;gBACA,KAAK;oBAAQ;wBACX,MAAM,cAAc;wBACpB,WAAW,OAAO,CAAC,EAAE,EAAE;wBACvB,gBAAgB;wBAChB;oBACF;gBACA,KAAK;oBAAO;wBACV,MAAM,cAAc;wBACpB,MAAM,YAAY,MAAM,MAAM,GAAG;wBACjC,WAAW,OAAO,CAAC,UAAU,EAAE;wBAC/B,gBAAgB;wBAChB;oBACF;YACF;QACF;kDACA;QAAC,MAAM,MAAM;KAAC;IAGhB,yBAAyB;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,MAAM;QAC/D;iCAAG;QAAC,MAAM,MAAM;KAAC;IAEjB,IAAI,WAAW;QACb,qBACE,4TAAC;YACC,WAAU;YACV,MAAK;YACL,cAAW;sBAEV,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,4TAAC;oBAAyB,OAAO;mBAAd;;;;;;;;;;IAI3B;IAEA,qBACE,4TAAC;QACC,WAAU;QACV,MAAK;QACL,cAAW;kBAEV,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,MAAM,WAAW,YAAY,KAAK,GAAG;YACrC,MAAM,YAAY,iBAAiB;YACnC,MAAM,YAAY,iBAAiB;YAEnC,yDAAyD;YACzD,MAAM,cACJ,QAAQ,WAAW,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO,IAC7C,CAAC;gBACC,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,OAAO;gBAC5D,OAAO;oBACL,iBAAiB,gBAAgB,IAAI;oBACrC,OAAO,OAAO,QAAQ,IAAI;oBAC1B,cAAc,gBAAgB,KAAK;oBACnC,eAAe,gBAAgB,MAAM;gBACvC;YACF,CAAC,MACD,CAAC;YAEP,qBACE,4TAAC;gBAEC,WAAU;;kCAEV,4TAAC,8HAAA,CAAA,SAAM;wBACL,KAAK,CAAC;4BACJ,WAAW,OAAO,CAAC,MAAM,GAAG;wBAC9B;wBACA,SAAS,CAAC,IAAM,gBAAgB,KAAK,GAAG,EAAE,OAAO;wBACjD,WAAW,CAAC,IAAM,cAAc,GAAG;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,QAAQ,IAAM,gBAAgB;wBAC9B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qOACA,uGACA,wCACA,aAAa,0BACb,aAAa,kCACb,kDAAkD;wBAClD,CAAC,QAAQ,WACP;wBAEJ,OAAO;4BACL,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4BACjC,mBAAmB;4BACnB,GAAI,QAAQ,UAAU,cAAc,CAAC,CAAC;wBACxC;wBACA,cAAc,CAAC;4BACb,IAAI,QAAQ,WAAW,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO,GAAG;gCACtD,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,OAAO;gCAC5D,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,gBAAgB,KAAK;4BAC/D;wBACF;wBACA,cAAc,CAAC;4BACb,IAAI,QAAQ,WAAW,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,OAAO,GAAG;gCACtD,MAAM,kBAAkB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,OAAO;gCAC5D,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,gBAAgB,IAAI;4BAC9D;wBACF;wBACA,cAAY,GAAG,KAAK,IAAI,CAAC,GAAG,EAC1B,aAAa,aACT,qBACA,aAAa,WACb,gBACA,aAAa,aACb,gBACA,gBACJ;wBACF,oBAAkB,CAAC,iBAAiB,EAAE,OAAO;;0CAG7C,4TAAC;gCAAI,WAAU;;;;;;0CAGf,4TAAC;gCAAI,WAAU;;;;;;0CAEf,4TAAC;gCAAI,WAAU;0CAEb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,gBAAgB,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,SAAS;gDACrD,qBACE,4TAAC;oDACC,WAAU;oDACV,eAAY;;;;;;4CAGlB,CAAC;;;;;;sDAEH,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,+HAAA,CAAA,kBAAe;0DACd,cAAA,4TAAC,+HAAA,CAAA,UAAO;;sEACN,4TAAC,+HAAA,CAAA,iBAAc;4DAAC,OAAO;sEACrB,cAAA,4TAAC;gEACC,WAAU;gEACV,OAAO;oEACL,SAAS;oEACT,iBAAiB;oEACjB,iBAAiB;oEACjB,UAAU;oEACV,WAAW;oEACX,SAAS;oEACT,YAAY;gEACd;gEACA,OACE,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,IAAI,GAAG;0EAGrC,KAAK,IAAI;;;;;;;;;;;wDAGb,KAAK,IAAI,CAAC,MAAM,GAAG,oBAClB,4TAAC,+HAAA,CAAA,iBAAc;sEACb,cAAA,4TAAC;gEAAE,WAAU;0EACV,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAW1B,4TAAC;gCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sFACA,mDACA,aAAa;;;;;;;;;;;;kCAMnB,4TAAC;wBAAK,IAAI,CAAC,iBAAiB,EAAE,OAAO;wBAAE,WAAU;;4BAC9C,aAAa,cAAc;4BAC3B,aAAa,YAAY;4BACzB,aAAa,cAAc;4BAC3B,aAAa,WAAW;4BACxB,aAAa,WAAW;4BACxB,aAAa,YAAY;;;;;;;;eAzHvB,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO;;;;;QA6HjC;;;;;;AAGN;GApQM;MAAA;uCAsQS", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/Gallery.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport useEmblaCarousel from \"embla-carousel-react\";\r\nimport { useCallback, useEffect, useRef, useState } from \"react\";\r\nimport { ChevronLeft, ChevronRight, Eye } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Image from \"next/image\";\r\n\r\n// Import PhotoSwipe styles\r\nimport \"photoswipe/dist/photoswipe.css\";\r\n\r\n// Import PhotoSwipe\r\nimport PhotoSwipeLightbox from \"photoswipe/lightbox\";\r\nimport PhotoSwipe from \"photoswipe\";\r\n\r\ninterface GalleryImage {\r\n  id: number;\r\n  url: string;\r\n  alt: string;\r\n  title: string;\r\n  description: string;\r\n}\r\n\r\ninterface GalleryProps {\r\n  gallery: {\r\n    title: string;\r\n    description: string;\r\n    enabled: boolean;\r\n    images: GalleryImage[];\r\n  };\r\n}\r\n\r\nconst Gallery = ({ gallery }: GalleryProps) => {\r\n  const galleryRef = useRef<HTMLDivElement>(null);\r\n  const [imageDimensions, setImageDimensions] = useState<{\r\n    [key: string]: { width: number; height: number };\r\n  }>({});\r\n  const [emblaRef, emblaApi] = useEmblaCarousel({\r\n    align: \"start\",\r\n    loop: true,\r\n  });\r\n\r\n  const scrollPrev = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollPrev();\r\n  }, [emblaApi]);\r\n\r\n  const scrollNext = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollNext();\r\n  }, [emblaApi]);\r\n\r\n  // Initialize PhotoSwipe\r\n  useEffect(() => {\r\n    let lightbox: PhotoSwipeLightbox | null = null;\r\n\r\n    if (\r\n      galleryRef.current &&\r\n      gallery.images.length > 0 &&\r\n      Object.keys(imageDimensions).length > 0\r\n    ) {\r\n      lightbox = new PhotoSwipeLightbox({\r\n        gallery: \"#photo-gallery\",\r\n        children: \"a\",\r\n        pswpModule: PhotoSwipe,\r\n        // Options\r\n        showHideAnimationType: \"fade\",\r\n        showAnimationDuration: 300,\r\n        hideAnimationDuration: 300,\r\n        zoom: true,\r\n        counter: true,\r\n        close: true,\r\n        imageClickAction: \"zoom\",\r\n        tapAction: \"zoom\",\r\n      });\r\n\r\n      lightbox.init();\r\n    }\r\n\r\n    return () => {\r\n      if (lightbox) {\r\n        lightbox.destroy();\r\n      }\r\n    };\r\n  }, [gallery.images, imageDimensions]);\r\n\r\n  // Preload images and get their real dimensions\r\n  useEffect(() => {\r\n    const loadImageDimensions = async () => {\r\n      const dimensions: { [key: string]: { width: number; height: number } } =\r\n        {};\r\n\r\n      await Promise.all(\r\n        gallery.images.map((image) => {\r\n          return new Promise<void>((resolve) => {\r\n            const img = new window.Image();\r\n            img.onload = () => {\r\n              dimensions[image.url] = {\r\n                width: img.naturalWidth,\r\n                height: img.naturalHeight,\r\n              };\r\n              resolve();\r\n            };\r\n            img.onerror = () => {\r\n              // Fallback dimensions if image fails to load\r\n              dimensions[image.url] = {\r\n                width: 1200,\r\n                height: 800,\r\n              };\r\n              resolve();\r\n            };\r\n            img.src = image.url;\r\n          });\r\n        })\r\n      );\r\n\r\n      setImageDimensions(dimensions);\r\n    };\r\n\r\n    if (gallery.images.length > 0) {\r\n      loadImageDimensions();\r\n    }\r\n  }, [gallery.images]);\r\n\r\n  if (!gallery.enabled) return null;\r\n\r\n  return (\r\n    <section className=\"mb-12 animate-fade-in\">\r\n      <div className=\"text-center mb-8 lg:mb-12\">\r\n        <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3\">\r\n          {gallery.title}\r\n        </h2>\r\n        <p className=\"text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed\">\r\n          {gallery.description}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"relative\" ref={galleryRef} id=\"photo-gallery\">\r\n        <div\r\n          className=\"overflow-hidden rounded-xl sm:rounded-2xl\"\r\n          ref={emblaRef}\r\n        >\r\n          <div className=\"flex\">\r\n            {gallery.images.map((image, index) => (\r\n              <div\r\n                key={image.id}\r\n                className=\"flex-[0_0_100%] min-w-0 px-1 sm:px-2\"\r\n                style={{ animationDelay: `${index * 0.1}s` }}\r\n              >\r\n                <div className=\"overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] transform-gpu group rounded-lg\">\r\n                  <a\r\n                    href={image.url}\r\n                    data-pswp-width={imageDimensions[image.url]?.width || 1200}\r\n                    data-pswp-height={imageDimensions[image.url]?.height || 800}\r\n                    data-pswp-caption={`<h4>${image.title}</h4><p>${image.description}</p>`}\r\n                    target=\"_blank\"\r\n                    rel=\"noreferrer\"\r\n                    className=\"block relative group cursor-pointer\"\r\n                  >\r\n                    <Image\r\n                      src={image.url}\r\n                      alt={image.alt}\r\n                      width={imageDimensions[image.url]?.width || 1200}\r\n                      height={imageDimensions[image.url]?.height || 800}\r\n                      className=\"w-full h-64 sm:h-80 lg:h-96 object-cover transition-transform duration-700 group-hover:scale-110\"\r\n                      priority={index === 0}\r\n                      sizes=\"(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw\"\r\n                    />\r\n                    {/* Enhanced overlay with better gradient */}\r\n                    <div className=\"absolute inset-0 bg-linear-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end\">\r\n                      <div className=\"p-4 sm:p-6 lg:p-8 text-white w-full transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500\">\r\n                        <h3 className=\"font-bold text-lg sm:text-xl lg:text-2xl mb-2 drop-shadow-lg\">\r\n                          {image.title}\r\n                        </h3>\r\n                        <p className=\"text-sm sm:text-base opacity-90 leading-relaxed drop-shadow-sm\">\r\n                          {image.description}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* View icon overlay */}\r\n                    <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\r\n                      <div className=\"bg-white/20 backdrop-blur-sm rounded-full p-3\">\r\n                        <Eye className=\"w-6 h-6 text-white\" />\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Image counter badge */}\r\n                    <div className=\"absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1\">\r\n                      <span className=\"text-white text-xs sm:text-sm font-medium\">\r\n                        {index + 1} / {gallery.images.length}\r\n                      </span>\r\n                    </div>\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Enhanced navigation buttons */}\r\n        <div className=\"flex justify-center mt-6 sm:mt-8 gap-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={scrollPrev}\r\n            className=\"w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 bg-primary/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-200 hover:scale-110 transform\"\r\n            aria-label=\"Imagem anterior\"\r\n          >\r\n            <ChevronLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={scrollNext}\r\n            className=\"w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 bg-primary/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-200 hover:scale-110 transform\"\r\n            aria-label=\"Próxima imagem\"\r\n          >\r\n            <ChevronRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Dots indicator */}\r\n        <div className=\"flex justify-center mt-4 gap-2\">\r\n          {gallery.images.map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-2 h-2 rounded-full bg-primary transition-all duration-300 hover:bg-primary cursor-pointer\"\r\n              onClick={() => emblaApi?.scrollTo(index)}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Gallery;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAKA,oBAAoB;AACpB;AACA;;;AAbA;;;;;;;;;AAgCA,MAAM,UAAU,CAAC,EAAE,OAAO,EAAgB;;IACxC,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAElD,CAAC;IACJ,MAAM,CAAC,UAAU,SAAS,GAAG,CAAA,GAAA,sRAAA,CAAA,UAAgB,AAAD,EAAE;QAC5C,OAAO;QACP,MAAM;IACR;IAEA,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;2CAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;0CAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;2CAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;0CAAG;QAAC;KAAS;IAEb,wBAAwB;IACxB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,WAAsC;YAE1C,IACE,WAAW,OAAO,IAClB,QAAQ,MAAM,CAAC,MAAM,GAAG,KACxB,OAAO,IAAI,CAAC,iBAAiB,MAAM,GAAG,GACtC;gBACA,WAAW,IAAI,6NAAA,CAAA,UAAkB,CAAC;oBAChC,SAAS;oBACT,UAAU;oBACV,YAAY,iNAAA,CAAA,UAAU;oBACtB,UAAU;oBACV,uBAAuB;oBACvB,uBAAuB;oBACvB,uBAAuB;oBACvB,MAAM;oBACN,SAAS;oBACT,OAAO;oBACP,kBAAkB;oBAClB,WAAW;gBACb;gBAEA,SAAS,IAAI;YACf;YAEA;qCAAO;oBACL,IAAI,UAAU;wBACZ,SAAS,OAAO;oBAClB;gBACF;;QACF;4BAAG;QAAC,QAAQ,MAAM;QAAE;KAAgB;IAEpC,+CAA+C;IAC/C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;yDAAsB;oBAC1B,MAAM,aACJ,CAAC;oBAEH,MAAM,QAAQ,GAAG,CACf,QAAQ,MAAM,CAAC,GAAG;iEAAC,CAAC;4BAClB,OAAO,IAAI;yEAAc,CAAC;oCACxB,MAAM,MAAM,IAAI,OAAO,KAAK;oCAC5B,IAAI,MAAM;iFAAG;4CACX,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG;gDACtB,OAAO,IAAI,YAAY;gDACvB,QAAQ,IAAI,aAAa;4CAC3B;4CACA;wCACF;;oCACA,IAAI,OAAO;iFAAG;4CACZ,6CAA6C;4CAC7C,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG;gDACtB,OAAO;gDACP,QAAQ;4CACV;4CACA;wCACF;;oCACA,IAAI,GAAG,GAAG,MAAM,GAAG;gCACrB;;wBACF;;oBAGF,mBAAmB;gBACrB;;YAEA,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7B;YACF;QACF;4BAAG;QAAC,QAAQ,MAAM;KAAC;IAEnB,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO;IAE7B,qBACE,4TAAC;QAAQ,WAAU;;0BACjB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,4TAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;;;;;;;0BAIxB,4TAAC;gBAAI,WAAU;gBAAW,KAAK;gBAAY,IAAG;;kCAC5C,4TAAC;wBACC,WAAU;wBACV,KAAK;kCAEL,cAAA,4TAAC;4BAAI,WAAU;sCACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,4TAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;8CAE3C,cAAA,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CACC,MAAM,MAAM,GAAG;4CACf,mBAAiB,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,SAAS;4CACtD,oBAAkB,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU;4CACxD,qBAAmB,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,QAAQ,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;4CACvE,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,4TAAC,+PAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,GAAG;oDACd,KAAK,MAAM,GAAG;oDACd,OAAO,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,SAAS;oDAC5C,QAAQ,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU;oDAC9C,WAAU;oDACV,UAAU,UAAU;oDACpB,OAAM;;;;;;8DAGR,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAG,WAAU;0EACX,MAAM,KAAK;;;;;;0EAEd,4TAAC;gEAAE,WAAU;0EACV,MAAM,WAAW;;;;;;;;;;;;;;;;;8DAMxB,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC,uRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKnB,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC;wDAAK,WAAU;;4DACb,QAAQ;4DAAE;4DAAI,QAAQ,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mCA7CvC,MAAM,EAAE;;;;;;;;;;;;;;;kCAwDrB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,4TAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,4TAAC,6SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,4TAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,4TAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,UAAU,SAAS;+BAF7B;;;;;;;;;;;;;;;;;;;;;;AASnB;GAzMM;;QAKyB,sRAAA,CAAA,UAAgB;;;KALzC;uCA2MS", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/Reviews.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport useEmblaCarousel from \"embla-carousel-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\n\r\ninterface Review {\r\n  id: number;\r\n  name: string;\r\n  photo: string;\r\n  comment: string;\r\n  rating: number;\r\n}\r\n\r\ninterface ReviewsProps {\r\n  reviews: {\r\n    title: string;\r\n    description: string;\r\n    enabled: boolean;\r\n    reviews: Review[];\r\n  };\r\n}\r\n\r\nconst Reviews = ({ reviews }: ReviewsProps) => {\r\n  const [emblaRef, emblaApi] = useEmblaCarousel({\r\n    align: \"start\",\r\n    containScroll: \"trimSnaps\",\r\n    dragFree: true,\r\n    loop: false,\r\n  });\r\n\r\n  const [canScrollPrev, setCanScrollPrev] = useState(false);\r\n  const [canScrollNext, setCanScrollNext] = useState(false);\r\n\r\n  const scrollPrev = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollPrev();\r\n  }, [emblaApi]);\r\n\r\n  const scrollNext = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollNext();\r\n  }, [emblaApi]);\r\n\r\n  const onSelect = useCallback(() => {\r\n    if (!emblaApi) return;\r\n    setCanScrollPrev(emblaApi.canScrollPrev());\r\n    setCanScrollNext(emblaApi.canScrollNext());\r\n  }, [emblaApi]);\r\n\r\n  useEffect(() => {\r\n    if (!emblaApi) return;\r\n    onSelect();\r\n    emblaApi.on(\"select\", onSelect);\r\n    emblaApi.on(\"reInit\", onSelect);\r\n  }, [emblaApi, onSelect]);\r\n\r\n  if (!reviews.enabled) return null;\r\n\r\n  return (\r\n    <section className=\"mb-12 animate-fade-in\">\r\n      <div className=\"text-center mb-8 lg:mb-12\">\r\n        <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3\">\r\n          {reviews.title}\r\n        </h2>\r\n        <p className=\"text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed\">\r\n          {reviews.description}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"relative\">\r\n        <div className=\"overflow-hidden rounded-xl\" ref={emblaRef}>\r\n          <div className=\"flex gap-4\">\r\n            {reviews.reviews.map((review, index) => (\r\n              <div\r\n                key={review.id}\r\n                className=\"flex-[0_0_90%] sm:flex-[0_0_85%] md:flex-[0_0_80%] lg:flex-[0_0_360px] min-w-0\"\r\n                style={{ animationDelay: `${index * 0.1}s` }}\r\n              >\r\n                <Card className=\"shadow-soft hover:shadow-strong transition-all duration-500 hover:scale-[1.02] transform-gpu group h-full\">\r\n                  <CardContent className=\"p-4 sm:p-6 h-full flex flex-col\">\r\n                    <div className=\"flex items-start space-x-3 sm:space-x-4 mb-4\">\r\n                      <Avatar className=\"w-10 h-10 sm:w-12 sm:h-12 shrink-0 ring-2 ring-primary/10 group-hover:ring-primary/20 transition-all duration-300\">\r\n                        <AvatarImage\r\n                          src={review.photo}\r\n                          alt={review.name}\r\n                          className=\"object-cover\"\r\n                        />\r\n                        <AvatarFallback className=\"bg-linear-to-r from-primary to-primary/80 text-primary-foreground font-bold text-sm sm:text-base\">\r\n                          {review.name.charAt(0)}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <div className=\"flex items-start justify-between gap-2 mb-2\">\r\n                          <h4 className=\"font-semibold text-foreground text-sm sm:text-base truncate\">\r\n                            {review.name}\r\n                          </h4>\r\n                          <div\r\n                            className=\"flex text-yellow-400 shrink-0\"\r\n                            aria-label={`${review.rating} estrelas`}\r\n                          >\r\n                            {[...Array(review.rating)].map((_, i) => (\r\n                              <i\r\n                                key={i}\r\n                                className=\"fas fa-star text-xs sm:text-sm group-hover:scale-110 transition-transform duration-300\"\r\n                                style={{ animationDelay: `${i * 0.1}s` }}\r\n                                aria-hidden=\"true\"\r\n                              ></i>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Review comment with better typography */}\r\n                    <blockquote className=\"flex-1 flex items-center\">\r\n                      <p className=\"text-muted-foreground text-xs sm:text-sm leading-relaxed italic\">\r\n                        &ldquo;{review.comment}&rdquo;\r\n                      </p>\r\n                    </blockquote>\r\n\r\n                    {/* Decorative quote mark */}\r\n                    <div className=\"mt-4 flex justify-end opacity-20 group-hover:opacity-40 transition-opacity duration-300\">\r\n                      <i className=\"fas fa-quote-right text-2xl text-primary\"></i>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation buttons */}\r\n        <div className=\"flex justify-center mt-8 gap-3\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={scrollPrev}\r\n            disabled={!canScrollPrev}\r\n            className=\"w-12 h-12 rounded-full p-0 bg-primary/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform\"\r\n            aria-label=\"Avaliação anterior\"\r\n          >\r\n            <ChevronLeft className=\"w-5 h-5\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={scrollNext}\r\n            disabled={!canScrollNext}\r\n            className=\"w-12 h-12 rounded-full p-0 bg-primary/80 backdrop-blur-sm hover:bg-primary hover:text-primary-foreground transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform\"\r\n            aria-label=\"Próxima avaliação\"\r\n          >\r\n            <ChevronRight className=\"w-5 h-5\" />\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Progress indicator */}\r\n        <div className=\"flex justify-center mt-4 gap-2\">\r\n          {reviews.reviews.map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-2 h-2 rounded-full bg-muted transition-all duration-300 hover:bg-primary cursor-pointer\"\r\n              onClick={() => emblaApi?.scrollTo(index)}\r\n            />\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Reviews;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAmBA,MAAM,UAAU,CAAC,EAAE,OAAO,EAAgB;;IACxC,MAAM,CAAC,UAAU,SAAS,GAAG,CAAA,GAAA,sRAAA,CAAA,UAAgB,AAAD,EAAE;QAC5C,OAAO;QACP,eAAe;QACf,UAAU;QACV,MAAM;IACR;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;2CAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;0CAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;2CAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;0CAAG;QAAC;KAAS;IAEb,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;yCAAE;YAC3B,IAAI,CAAC,UAAU;YACf,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,aAAa;QACzC;wCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,UAAU;YACf;YACA,SAAS,EAAE,CAAC,UAAU;YACtB,SAAS,EAAE,CAAC,UAAU;QACxB;4BAAG;QAAC;QAAU;KAAS;IAEvB,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO;IAE7B,qBACE,4TAAC;QAAQ,WAAU;;0BACjB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK;;;;;;kCAEhB,4TAAC;wBAAE,WAAU;kCACV,QAAQ,WAAW;;;;;;;;;;;;0BAIxB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;wBAA6B,KAAK;kCAC/C,cAAA,4TAAC;4BAAI,WAAU;sCACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,4TAAC;oCAEC,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCAAC;8CAE3C,cAAA,4TAAC,4HAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,8HAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,4TAAC,8HAAA,CAAA,cAAW;oEACV,KAAK,OAAO,KAAK;oEACjB,KAAK,OAAO,IAAI;oEAChB,WAAU;;;;;;8EAEZ,4TAAC,8HAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;sEAGxB,4TAAC;4DAAI,WAAU;sEACb,cAAA,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAG,WAAU;kFACX,OAAO,IAAI;;;;;;kFAEd,4TAAC;wEACC,WAAU;wEACV,cAAY,GAAG,OAAO,MAAM,CAAC,SAAS,CAAC;kFAEtC;+EAAI,MAAM,OAAO,MAAM;yEAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACjC,4TAAC;gFAEC,WAAU;gFACV,OAAO;oFAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gFAAC;gFACvC,eAAY;+EAHP;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAYjB,4TAAC;oDAAW,WAAU;8DACpB,cAAA,4TAAC;wDAAE,WAAU;;4DAAkE;4DACrE,OAAO,OAAO;4DAAC;;;;;;;;;;;;8DAK3B,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;mCAhDd,OAAO,EAAE;;;;;;;;;;;;;;;kCA0DtB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,cAAW;0CAEX,cAAA,4TAAC,2SAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,cAAW;0CAEX,cAAA,4TAAC,6SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,4TAAC;wBAAI,WAAU;kCACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,4TAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,UAAU,SAAS;+BAF7B;;;;;;;;;;;;;;;;;;;;;;AASnB;GAhJM;;QACyB,sRAAA,CAAA,UAAgB;;;KADzC;uCAkJS", "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/sections/GenericSection.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport useEmblaCarousel from \"embla-carousel-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\nimport { ChevronLeft, ChevronRight, Phone } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\n\r\ninterface SectionItem {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  image: string;\r\n  primaryButton: {\r\n    icon: string;\r\n    url: string;\r\n  };\r\n  secondaryButton: {\r\n    icon: string;\r\n    url: string;\r\n  };\r\n}\r\n\r\ninterface SectionData {\r\n  title: string;\r\n  description: string;\r\n  enabled: boolean;\r\n  items: SectionItem[];\r\n}\r\n\r\ninterface GenericSectionProps {\r\n  sectionData: SectionData;\r\n  layout?: \"grid\" | \"carousel\";\r\n  primaryButtonText?: string;\r\n  secondaryButtonText?: string;\r\n  showBadge?: boolean;\r\n  sectionType?: \"features\" | \"services\" | \"generic\";\r\n  colors?: {\r\n    background?: string;\r\n    linkText?: string;\r\n    primary?: string;\r\n    secondary?: string;\r\n    socialIconBackground?: string;\r\n  };\r\n}\r\n\r\nconst GenericSection = ({\r\n  sectionData,\r\n  layout = \"grid\",\r\n  primaryButtonText = \"Ver Mais\",\r\n  secondaryButtonText = \"Contato\",\r\n  showBadge = false,\r\n  sectionType = \"generic\",\r\n  colors,\r\n}: GenericSectionProps) => {\r\n  const [emblaRef, emblaApi] = useEmblaCarousel({\r\n    align: \"start\",\r\n    containScroll: \"trimSnaps\",\r\n    dragFree: false,\r\n    loop: false,\r\n    skipSnaps: false,\r\n  });\r\n\r\n  const [canScrollPrev, setCanScrollPrev] = useState(false);\r\n  const [canScrollNext, setCanScrollNext] = useState(false);\r\n  const [selectedIndex, setSelectedIndex] = useState(0);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  const scrollPrev = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollPrev();\r\n  }, [emblaApi]);\r\n\r\n  const scrollNext = useCallback(() => {\r\n    if (emblaApi) emblaApi.scrollNext();\r\n  }, [emblaApi]);\r\n\r\n  const onSelect = useCallback(() => {\r\n    if (!emblaApi) return;\r\n    setCanScrollPrev(emblaApi.canScrollPrev());\r\n    setCanScrollNext(emblaApi.canScrollNext());\r\n    setSelectedIndex(emblaApi.selectedScrollSnap());\r\n  }, [emblaApi]);\r\n\r\n  useEffect(() => {\r\n    if (!emblaApi) return;\r\n    onSelect();\r\n    emblaApi.on(\"select\", onSelect);\r\n    emblaApi.on(\"reInit\", onSelect);\r\n\r\n    // Set loading to false after carousel is initialized\r\n    const timer = setTimeout(() => setIsLoading(false), 100);\r\n    return () => clearTimeout(timer);\r\n  }, [emblaApi, onSelect]);\r\n\r\n  if (!sectionData.enabled) return null;\r\n\r\n  const handleButtonClick = (url: string, event?: React.MouseEvent) => {\r\n    if (event) {\r\n      event.preventDefault();\r\n      event.stopPropagation();\r\n    }\r\n\r\n    if (url && url !== \"#\" && url.startsWith(\"http\")) {\r\n      window.open(url, \"_blank\", \"noopener,noreferrer\");\r\n    }\r\n  };\r\n\r\n  const renderCard = (item: SectionItem) => {\r\n    const hasPrimaryButton =\r\n      item.primaryButton.url && item.primaryButton.url !== \"#\";\r\n    const hasSecondaryButton =\r\n      item.secondaryButton.url && item.secondaryButton.url !== \"#\";\r\n\r\n    // Create dynamic styles using API colors\r\n    const primaryButtonStyle = colors?.primary\r\n      ? ({\r\n          backgroundColor: `${colors.primary}99`, // 60% opacity\r\n          color: colors.linkText || \"#ffffff\",\r\n          \"--hover-bg\": colors.primary,\r\n        } as React.CSSProperties)\r\n      : {};\r\n\r\n    const secondaryButtonStyle = colors?.secondary\r\n      ? ({\r\n          backgroundColor: `${colors.secondary}1A`, // 10% opacity\r\n          borderColor: `${colors.secondary}4D`, // 30% opacity\r\n          color: colors.linkText || \"#ffffff\",\r\n          \"--hover-bg\": `${colors.secondary}33`, // 20% opacity\r\n          \"--hover-border\": `${colors.secondary}80`, // 50% opacity\r\n        } as React.CSSProperties)\r\n      : {};\r\n\r\n    const badgeStyle = colors?.primary\r\n      ? ({\r\n          backgroundColor: `${colors.primary}E6`, // 90% opacity\r\n        } as React.CSSProperties)\r\n      : {};\r\n\r\n    return (\r\n      <div\r\n        key={item.id}\r\n        className=\"relative overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform-gpu group rounded-xl h-96 sm:h-80 lg:h-96\"\r\n      >\r\n        <Image\r\n          src={item.image}\r\n          alt={item.title}\r\n          fill\r\n          className=\"object-cover transition-transform duration-700 group-hover:scale-110\"\r\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n          priority={false}\r\n        />\r\n        {/* Enhanced gradient overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent\"></div>\r\n\r\n        {/* Optional badge for services */}\r\n        {showBadge && (\r\n          <div className=\"absolute top-6 right-6\">\r\n            <div\r\n              className=\"w-10 h-10 backdrop-blur-sm rounded-full flex items-center justify-center transform transition-all duration-300 group-hover:scale-110 shadow-lg\"\r\n              style={\r\n                colors?.primary\r\n                  ? badgeStyle\r\n                  : { backgroundColor: \"rgba(59, 130, 246, 0.9)\" }\r\n              }\r\n            >\r\n              <span className=\"text-white text-base font-bold\">{item.id}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Content positioned at bottom like Flutter version */}\r\n        <div className=\"absolute bottom-0 left-0 right-0 p-6 sm:p-8\">\r\n          <h3 className=\"text-white font-bold text-xl sm:text-2xl mb-2 drop-shadow-lg\">\r\n            {item.title}\r\n          </h3>\r\n          <p className=\"text-white/90 text-sm sm:text-base mb-4 leading-relaxed drop-shadow-sm line-clamp-3\">\r\n            {item.description}\r\n          </p>\r\n\r\n          <div className=\"flex gap-3\">\r\n            {hasPrimaryButton && (\r\n              <Button\r\n                variant=\"default\"\r\n                size=\"lg\"\r\n                className=\"flex-1 h-14 backdrop-blur-sm hover:scale-105 transform transition-all duration-300 shadow-lg hover:shadow-xl group/btn border-0 rounded-2xl\"\r\n                style={\r\n                  colors?.primary\r\n                    ? primaryButtonStyle\r\n                    : {\r\n                        backgroundColor: \"rgba(59, 130, 246, 0.6)\",\r\n                        color: \"#ffffff\",\r\n                      }\r\n                }\r\n                onClick={(e) => handleButtonClick(item.primaryButton.url, e)}\r\n                onMouseEnter={(e) => {\r\n                  if (colors?.primary) {\r\n                    e.currentTarget.style.backgroundColor = colors.primary;\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (colors?.primary) {\r\n                    e.currentTarget.style.backgroundColor = `${colors.primary}99`;\r\n                  }\r\n                }}\r\n              >\r\n                {primaryButtonText}\r\n              </Button>\r\n            )}\r\n            {hasSecondaryButton && (\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"lg\"\r\n                className={`${\r\n                  hasPrimaryButton ? \"w-14 h-14 p-0\" : \"flex-1\"\r\n                } backdrop-blur-sm hover:scale-105 transform transition-all duration-300 group/btn shadow-lg rounded-2xl`}\r\n                style={\r\n                  colors?.secondary\r\n                    ? secondaryButtonStyle\r\n                    : {\r\n                        backgroundColor: \"rgba(255, 255, 255, 0.1)\",\r\n                        borderColor: \"rgba(255, 255, 255, 0.3)\",\r\n                        color: \"#ffffff\",\r\n                      }\r\n                }\r\n                onClick={(e) => handleButtonClick(item.secondaryButton.url, e)}\r\n                onMouseEnter={(e) => {\r\n                  if (colors?.secondary) {\r\n                    e.currentTarget.style.backgroundColor = `${colors.secondary}33`;\r\n                    e.currentTarget.style.borderColor = `${colors.secondary}80`;\r\n                  }\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  if (colors?.secondary) {\r\n                    e.currentTarget.style.backgroundColor = `${colors.secondary}1A`;\r\n                    e.currentTarget.style.borderColor = `${colors.secondary}4D`;\r\n                  }\r\n                }}\r\n              >\r\n                {item.secondaryButton.icon ? (\r\n                  <i\r\n                    className={`${item.secondaryButton.icon} ${\r\n                      hasPrimaryButton ? \"text-lg\" : \"mr-2\"\r\n                    } group-hover/btn:scale-110 transition-transform duration-300`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                ) : (\r\n                  <Phone\r\n                    className={`${\r\n                      hasPrimaryButton ? \"w-5 h-5\" : \"w-4 h-4 mr-2\"\r\n                    }`}\r\n                  />\r\n                )}\r\n                {!hasPrimaryButton && secondaryButtonText}\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  const renderGridLayout = () => (\r\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n      {sectionData.items.map((item, index) => (\r\n        <div\r\n          key={item.id}\r\n          className=\"animate-fade-in\"\r\n          style={{ animationDelay: `${index * 0.15}s` }}\r\n        >\r\n          {renderCard(item)}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n\r\n  const renderCarouselLayout = () => (\r\n    <div className=\"relative\">\r\n      <div\r\n        className={`overflow-hidden rounded-2xl transition-opacity duration-300${\r\n          isLoading ? \"opacity-0\" : \"opacity-100\"\r\n        }`}\r\n        ref={emblaRef}\r\n      >\r\n        <div className=\"flex gap-4 lg:gap-6\">\r\n          {sectionData.items.map((item, index) => (\r\n            <div\r\n              key={item.id}\r\n              className=\"flex-[0_0_90%] sm:flex-[0_0_85%] md:flex-[0_0_80%] lg:flex-[0_0_360px] min-w-0\"\r\n              style={{ animationDelay: `${index * 0.1}s` }}\r\n            >\r\n              {renderCard(item)}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation buttons */}\r\n      <div className=\"flex justify-center mt-8 gap-3\">\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={scrollPrev}\r\n          disabled={!canScrollPrev}\r\n          className=\"w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform\"\r\n          style={\r\n            colors?.primary\r\n              ? {\r\n                  backgroundColor: `${colors.primary}CC`, // 80% opacity\r\n                  color: colors.linkText || \"#ffffff\",\r\n                  borderColor: \"transparent\",\r\n                }\r\n              : {\r\n                  backgroundColor: \"rgba(59, 130, 246, 0.8)\",\r\n                  color: \"#ffffff\",\r\n                  borderColor: \"transparent\",\r\n                }\r\n          }\r\n          onMouseEnter={(e) => {\r\n            if (colors?.primary && !e.currentTarget.disabled) {\r\n              e.currentTarget.style.backgroundColor = colors.primary;\r\n            }\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            if (colors?.primary && !e.currentTarget.disabled) {\r\n              e.currentTarget.style.backgroundColor = `${colors.primary}CC`;\r\n            }\r\n          }}\r\n          aria-label={`${sectionType} anterior`}\r\n        >\r\n          <ChevronLeft className=\"w-5 h-5\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={scrollNext}\r\n          disabled={!canScrollNext}\r\n          className=\"w-12 h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 disabled:opacity-30 disabled:cursor-not-allowed hover:scale-110 transform\"\r\n          style={\r\n            colors?.primary\r\n              ? {\r\n                  backgroundColor: `${colors.primary}CC`, // 80% opacity\r\n                  color: colors.linkText || \"#ffffff\",\r\n                  borderColor: \"transparent\",\r\n                }\r\n              : {\r\n                  backgroundColor: \"rgba(59, 130, 246, 0.8)\",\r\n                  color: \"#ffffff\",\r\n                  borderColor: \"transparent\",\r\n                }\r\n          }\r\n          onMouseEnter={(e) => {\r\n            if (colors?.primary && !e.currentTarget.disabled) {\r\n              e.currentTarget.style.backgroundColor = colors.primary;\r\n            }\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            if (colors?.primary && !e.currentTarget.disabled) {\r\n              e.currentTarget.style.backgroundColor = `${colors.primary}CC`;\r\n            }\r\n          }}\r\n          aria-label={`Próximo ${sectionType}`}\r\n        >\r\n          <ChevronRight className=\"w-5 h-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Progress indicator with active state like Flutter */}\r\n      <div className=\"flex justify-center mt-4 gap-2\">\r\n        {sectionData.items.map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"w-2 h-2 rounded-full transition-all duration-300 cursor-pointer\"\r\n            style={{\r\n              backgroundColor: colors?.primary || \"#3b82f6\",\r\n              opacity: selectedIndex === index ? 1 : 0.4,\r\n              transform: selectedIndex === index ? \"scale(1.25)\" : \"scale(1)\",\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              if (selectedIndex !== index) {\r\n                e.currentTarget.style.opacity = \"0.6\";\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              if (selectedIndex !== index) {\r\n                e.currentTarget.style.opacity = \"0.4\";\r\n              }\r\n            }}\r\n            onClick={() => emblaApi?.scrollTo(index)}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <section className=\"mb-12 animate-fade-in\">\r\n      <div className=\"text-center mb-8 lg:mb-12\">\r\n        <h2 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-foreground mb-3\">\r\n          {sectionData.title}\r\n        </h2>\r\n        <p className=\"text-muted-foreground max-w-md lg:max-w-2xl mx-auto text-sm sm:text-base leading-relaxed\">\r\n          {sectionData.description}\r\n        </p>\r\n      </div>\r\n\r\n      {layout === \"grid\" ? renderGridLayout() : renderCarouselLayout()}\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default GenericSection;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;;AAwCA,MAAM,iBAAiB,CAAC,EACtB,WAAW,EACX,SAAS,MAAM,EACf,oBAAoB,UAAU,EAC9B,sBAAsB,SAAS,EAC/B,YAAY,KAAK,EACjB,cAAc,SAAS,EACvB,MAAM,EACc;;IACpB,MAAM,CAAC,UAAU,SAAS,GAAG,CAAA,GAAA,sRAAA,CAAA,UAAgB,AAAD,EAAE;QAC5C,OAAO;QACP,eAAe;QACf,UAAU;QACV,MAAM;QACN,WAAW;IACb;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;iDAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B,IAAI,UAAU,SAAS,UAAU;QACnC;iDAAG;QAAC;KAAS;IAEb,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;gDAAE;YAC3B,IAAI,CAAC,UAAU;YACf,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,aAAa;YACvC,iBAAiB,SAAS,kBAAkB;QAC9C;+CAAG;QAAC;KAAS;IAEb,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU;YACf;YACA,SAAS,EAAE,CAAC,UAAU;YACtB,SAAS,EAAE,CAAC,UAAU;YAEtB,qDAAqD;YACrD,MAAM,QAAQ;kDAAW,IAAM,aAAa;iDAAQ;YACpD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAU;KAAS;IAEvB,IAAI,CAAC,YAAY,OAAO,EAAE,OAAO;IAEjC,MAAM,oBAAoB,CAAC,KAAa;QACtC,IAAI,OAAO;YACT,MAAM,cAAc;YACpB,MAAM,eAAe;QACvB;QAEA,IAAI,OAAO,QAAQ,OAAO,IAAI,UAAU,CAAC,SAAS;YAChD,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,mBACJ,KAAK,aAAa,CAAC,GAAG,IAAI,KAAK,aAAa,CAAC,GAAG,KAAK;QACvD,MAAM,qBACJ,KAAK,eAAe,CAAC,GAAG,IAAI,KAAK,eAAe,CAAC,GAAG,KAAK;QAE3D,yCAAyC;QACzC,MAAM,qBAAqB,QAAQ,UAC9B;YACC,iBAAiB,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;YACtC,OAAO,OAAO,QAAQ,IAAI;YAC1B,cAAc,OAAO,OAAO;QAC9B,IACA,CAAC;QAEL,MAAM,uBAAuB,QAAQ,YAChC;YACC,iBAAiB,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;YACxC,aAAa,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;YACpC,OAAO,OAAO,QAAQ,IAAI;YAC1B,cAAc,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;YACrC,kBAAkB,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;QAC3C,IACA,CAAC;QAEL,MAAM,aAAa,QAAQ,UACtB;YACC,iBAAiB,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;QACxC,IACA,CAAC;QAEL,qBACE,4TAAC;YAEC,WAAU;;8BAEV,4TAAC,+PAAA,CAAA,UAAK;oBACJ,KAAK,KAAK,KAAK;oBACf,KAAK,KAAK,KAAK;oBACf,IAAI;oBACJ,WAAU;oBACV,OAAM;oBACN,UAAU;;;;;;8BAGZ,4TAAC;oBAAI,WAAU;;;;;;gBAGd,2BACC,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBACC,WAAU;wBACV,OACE,QAAQ,UACJ,aACA;4BAAE,iBAAiB;wBAA0B;kCAGnD,cAAA,4TAAC;4BAAK,WAAU;sCAAkC,KAAK,EAAE;;;;;;;;;;;;;;;;8BAM/D,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCACX,KAAK,KAAK;;;;;;sCAEb,4TAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;sCAGnB,4TAAC;4BAAI,WAAU;;gCACZ,kCACC,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OACE,QAAQ,UACJ,qBACA;wCACE,iBAAiB;wCACjB,OAAO;oCACT;oCAEN,SAAS,CAAC,IAAM,kBAAkB,KAAK,aAAa,CAAC,GAAG,EAAE;oCAC1D,cAAc,CAAC;wCACb,IAAI,QAAQ,SAAS;4CACnB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,OAAO;wCACxD;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,QAAQ,SAAS;4CACnB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;wCAC/D;oCACF;8CAEC;;;;;;gCAGJ,oCACC,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,GACT,mBAAmB,kBAAkB,SACtC,uGAAuG,CAAC;oCACzG,OACE,QAAQ,YACJ,uBACA;wCACE,iBAAiB;wCACjB,aAAa;wCACb,OAAO;oCACT;oCAEN,SAAS,CAAC,IAAM,kBAAkB,KAAK,eAAe,CAAC,GAAG,EAAE;oCAC5D,cAAc,CAAC;wCACb,IAAI,QAAQ,WAAW;4CACrB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;4CAC/D,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;wCAC7D;oCACF;oCACA,cAAc,CAAC;wCACb,IAAI,QAAQ,WAAW;4CACrB,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;4CAC/D,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,GAAG,OAAO,SAAS,CAAC,EAAE,CAAC;wCAC7D;oCACF;;wCAEC,KAAK,eAAe,CAAC,IAAI,iBACxB,4TAAC;4CACC,WAAW,GAAG,KAAK,eAAe,CAAC,IAAI,CAAC,CAAC,EACvC,mBAAmB,YAAY,OAChC,4DAA4D,CAAC;4CAC9D,eAAY;;;;;iEAGd,4TAAC,2RAAA,CAAA,QAAK;4CACJ,WAAW,GACT,mBAAmB,YAAY,gBAC/B;;;;;;wCAGL,CAAC,oBAAoB;;;;;;;;;;;;;;;;;;;;WAhHzB,KAAK,EAAE;;;;;IAuHlB;IAEA,MAAM,mBAAmB,kBACvB,4TAAC;YAAI,WAAU;sBACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,4TAAC;oBAEC,WAAU;oBACV,OAAO;wBAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;oBAAC;8BAE3C,WAAW;mBAJP,KAAK,EAAE;;;;;;;;;;IAUpB,MAAM,uBAAuB,kBAC3B,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBACC,WAAW,CAAC,2DAA2D,EACrE,YAAY,cAAc,eAC1B;oBACF,KAAK;8BAEL,cAAA,4TAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,4TAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;gCAAC;0CAE1C,WAAW;+BAJP,KAAK,EAAE;;;;;;;;;;;;;;;8BAWpB,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;4BACV,OACE,QAAQ,UACJ;gCACE,iBAAiB,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;gCACtC,OAAO,OAAO,QAAQ,IAAI;gCAC1B,aAAa;4BACf,IACA;gCACE,iBAAiB;gCACjB,OAAO;gCACP,aAAa;4BACf;4BAEN,cAAc,CAAC;gCACb,IAAI,QAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE;oCAChD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,OAAO;gCACxD;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,QAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE;oCAChD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;gCAC/D;4BACF;4BACA,cAAY,GAAG,YAAY,SAAS,CAAC;sCAErC,cAAA,4TAAC,2SAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,4TAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC;4BACX,WAAU;4BACV,OACE,QAAQ,UACJ;gCACE,iBAAiB,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;gCACtC,OAAO,OAAO,QAAQ,IAAI;gCAC1B,aAAa;4BACf,IACA;gCACE,iBAAiB;gCACjB,OAAO;gCACP,aAAa;4BACf;4BAEN,cAAc,CAAC;gCACb,IAAI,QAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE;oCAChD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,OAAO,OAAO;gCACxD;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,QAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,EAAE;oCAChD,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC;gCAC/D;4BACF;4BACA,cAAY,CAAC,QAAQ,EAAE,aAAa;sCAEpC,cAAA,4TAAC,6SAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,4TAAC;oBAAI,WAAU;8BACZ,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,sBACzB,4TAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,iBAAiB,QAAQ,WAAW;gCACpC,SAAS,kBAAkB,QAAQ,IAAI;gCACvC,WAAW,kBAAkB,QAAQ,gBAAgB;4BACvD;4BACA,cAAc,CAAC;gCACb,IAAI,kBAAkB,OAAO;oCAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gCAClC;4BACF;4BACA,cAAc,CAAC;gCACb,IAAI,kBAAkB,OAAO;oCAC3B,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gCAClC;4BACF;4BACA,SAAS,IAAM,UAAU,SAAS;2BAjB7B;;;;;;;;;;;;;;;;IAwBf,qBACE,4TAAC;QAAQ,WAAU;;0BACjB,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAG,WAAU;kCACX,YAAY,KAAK;;;;;;kCAEpB,4TAAC;wBAAE,WAAU;kCACV,YAAY,WAAW;;;;;;;;;;;;YAI3B,WAAW,SAAS,qBAAqB;;;;;;;AAGhD;GA1WM;;QASyB,sRAAA,CAAA,UAAgB;;;KATzC;uCA4WS", "debugId": null}}, {"offset": {"line": 3147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/components/user-profile.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { UserProfile as UserProfileType } from \"@/types/user\";\nimport { cn } from \"@/lib/utils\";\nimport { ThemeToggle } from \"./theme-toggle\";\nimport { getButtonConfig } from \"@/lib/buttonUtils\";\n\n// Import section components\nimport UserProfileSection from \"./sections/UserProfile\";\nimport SocialMedia from \"./sections/SocialMedia\";\nimport LinksSection from \"./sections/LinksSection\";\nimport Gallery from \"./sections/Gallery\";\nimport Reviews from \"./sections/Reviews\";\nimport GenericSection from \"./sections/GenericSection\";\n// import Location from \"./sections/Location\";\n\ninterface UserProfileProps {\n  profile: UserProfileType;\n  className?: string;\n}\n\nexport function UserProfile({ profile, className }: UserProfileProps) {\n  // Use the correct Firebase data structure\n  const links = profile.links || [];\n  const socialLinks = profile.socialMedia || [];\n  const colors = profile.settings?.colors;\n\n  const containerStyle = {\n    // backgroundColor: colors?.background || undefined,\n    color: colors?.linkText || undefined,\n  };\n\n  return (\n    <div\n      className={cn(\n        \"min-h-screen w-full bg-background text-foreground relative\",\n        className\n      )}\n      style={containerStyle}\n    >\n      {/* Floating Theme Toggle */}\n      <div className=\"fixed top-4 right-4 z-50\">\n        <ThemeToggle />\n      </div>\n\n      <div className=\"mx-auto max-w-full px-4 py-8 flex flex-col items-center\">\n        {/* Profile Header Section */}\n        {profile.user && (\n          <section\n            className=\"max-w-xl mx-auto mb-8\"\n            aria-label=\"Profile Header\"\n          >\n            <UserProfileSection user={profile.user} />\n          </section>\n        )}\n\n        {/* Social Media Section */}\n        {socialLinks && socialLinks.length > 0 && (\n          <section\n            className=\"max-w-xl mx-auto mb-8\"\n            aria-label=\"Social Media Links\"\n          >\n            <SocialMedia\n              socialMedia={socialLinks}\n              size=\"md\"\n              variant=\"default\"\n              colors={colors}\n            />\n          </section>\n        )}\n\n        {/* Links Section */}\n        {links && links.length > 0 && (\n          <section\n            className=\"max-w-xl mx-auto mb-8\"\n            aria-label=\"Navigation Links\"\n          >\n            <LinksSection links={links} colors={colors} />\n          </section>\n        )}\n\n        {/* Gallery Section */}\n        {profile.gallery && profile.gallery.enabled && (\n          <section className=\"max-w-xl mx-auto mb-8\" aria-label=\"Photo Gallery\">\n            <Gallery gallery={profile.gallery} />\n          </section>\n        )}\n\n        {/* Features Section */}\n        {profile.featuresSection && profile.featuresSection.enabled && (\n          <section\n            className=\"max-w-full mx-auto mb-8 text-center\"\n            aria-label=\"Features Showcase\"\n          >\n            <GenericSection\n              sectionData={profile.featuresSection}\n              layout=\"carousel\"\n              {...getButtonConfig(\n                \"features\",\n                profile.featuresSection.buttonConfig\n              )}\n              sectionType=\"features\"\n              colors={colors}\n            />\n          </section>\n        )}\n\n        {/* Services Section */}\n        {profile.servicesSection && profile.servicesSection.enabled && (\n          <section\n            className=\"max-w-full mx-auto mb-8 text-center\"\n            aria-label=\"Services Offered\"\n          >\n            <GenericSection\n              sectionData={profile.servicesSection}\n              layout=\"carousel\"\n              {...getButtonConfig(\n                \"services\",\n                profile.servicesSection.buttonConfig\n              )}\n              sectionType=\"services\"\n              colors={colors}\n            />\n          </section>\n        )}\n\n        {/* Generic Content Section */}\n        {profile.genericSection && profile.genericSection.enabled && (\n          <section\n            className=\"max-w-full mx-auto mb-8 text-center\"\n            aria-label=\"Additional Content\"\n          >\n            <GenericSection\n              sectionData={profile.genericSection}\n              layout=\"carousel\"\n              {...getButtonConfig(\n                \"generic\",\n                profile.genericSection.buttonConfig\n              )}\n              sectionType=\"generic\"\n              colors={colors}\n            />\n          </section>\n        )}\n\n        {/* Reviews Section */}\n        {profile.reviews && profile.reviews.enabled && (\n          <section\n            className=\"max-w-full mx-auto mb-8\"\n            aria-label=\"Customer Reviews\"\n          >\n            <Reviews reviews={profile.reviews} />\n          </section>\n        )}\n\n        {/* Location Section - if location data exists */}\n        {/* {profile.location && profile.location.enabled && (\n          <section className=\"max-w-xl mx-auto mb-8\" aria-label=\"Location Information\">\n            <Location locationData={profile.location} />\n          </section>\n        )} */}\n\n        {/* Footer Section */}\n        <section className=\"max-w-xl mx-auto\" aria-label=\"Footer\">\n          <div className=\"text-center mt-12 pt-8 border-t border-gray-200 dark:border-gray-700\">\n            <p className=\"text-xs opacity-50\">Created with AvencaLink</p>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AAEA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;AAsBO,SAAS,YAAY,EAAE,OAAO,EAAE,SAAS,EAAoB;IAClE,0CAA0C;IAC1C,MAAM,QAAQ,QAAQ,KAAK,IAAI,EAAE;IACjC,MAAM,cAAc,QAAQ,WAAW,IAAI,EAAE;IAC7C,MAAM,SAAS,QAAQ,QAAQ,EAAE;IAEjC,MAAM,iBAAiB;QACrB,oDAAoD;QACpD,OAAO,QAAQ,YAAY;IAC7B;IAEA,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAEF,OAAO;;0BAGP,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,iIAAA,CAAA,cAAW;;;;;;;;;;0BAGd,4TAAC;gBAAI,WAAU;;oBAEZ,QAAQ,IAAI,kBACX,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,yIAAA,CAAA,UAAkB;4BAAC,MAAM,QAAQ,IAAI;;;;;;;;;;;oBAKzC,eAAe,YAAY,MAAM,GAAG,mBACnC,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,yIAAA,CAAA,UAAW;4BACV,aAAa;4BACb,MAAK;4BACL,SAAQ;4BACR,QAAQ;;;;;;;;;;;oBAMb,SAAS,MAAM,MAAM,GAAG,mBACvB,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,0IAAA,CAAA,UAAY;4BAAC,OAAO;4BAAO,QAAQ;;;;;;;;;;;oBAKvC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,OAAO,kBACzC,4TAAC;wBAAQ,WAAU;wBAAwB,cAAW;kCACpD,cAAA,4TAAC,qIAAA,CAAA,UAAO;4BAAC,SAAS,QAAQ,OAAO;;;;;;;;;;;oBAKpC,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,OAAO,kBACzD,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,4IAAA,CAAA,UAAc;4BACb,aAAa,QAAQ,eAAe;4BACpC,QAAO;4BACN,GAAG,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAChB,YACA,QAAQ,eAAe,CAAC,YAAY,CACrC;4BACD,aAAY;4BACZ,QAAQ;;;;;;;;;;;oBAMb,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,OAAO,kBACzD,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,4IAAA,CAAA,UAAc;4BACb,aAAa,QAAQ,eAAe;4BACpC,QAAO;4BACN,GAAG,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAChB,YACA,QAAQ,eAAe,CAAC,YAAY,CACrC;4BACD,aAAY;4BACZ,QAAQ;;;;;;;;;;;oBAMb,QAAQ,cAAc,IAAI,QAAQ,cAAc,CAAC,OAAO,kBACvD,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,4IAAA,CAAA,UAAc;4BACb,aAAa,QAAQ,cAAc;4BACnC,QAAO;4BACN,GAAG,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAChB,WACA,QAAQ,cAAc,CAAC,YAAY,CACpC;4BACD,aAAY;4BACZ,QAAQ;;;;;;;;;;;oBAMb,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,OAAO,kBACzC,4TAAC;wBACC,WAAU;wBACV,cAAW;kCAEX,cAAA,4TAAC,qIAAA,CAAA,UAAO;4BAAC,SAAS,QAAQ,OAAO;;;;;;;;;;;kCAYrC,4TAAC;wBAAQ,WAAU;wBAAmB,cAAW;kCAC/C,cAAA,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;KAtJgB", "debugId": null}}, {"offset": {"line": 3384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Dev/AVENCA%20DIGITAL/CLIENTES/LINKTREE/WEB/json-link-canvas/avencalink/app/%5Busername%5D/user-profile-client.tsx"], "sourcesContent": ["'use client'\n\nimport { UserProfile as UserProfileType } from '@/types/user'\nimport { UserProfile } from '@/components/user-profile'\n\ninterface UserProfileClientProps {\n  initialProfile: UserProfileType\n}\n\nexport function UserProfileClient({ initialProfile }: UserProfileClientProps) {\n  // Simplified version - just render the profile directly\n  return <UserProfile profile={initialProfile} />\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AASO,SAAS,kBAAkB,EAAE,cAAc,EAA0B;IAC1E,wDAAwD;IACxD,qBAAO,4TAAC,iIAAA,CAAA,cAAW;QAAC,SAAS;;;;;;AAC/B;KAHgB", "debugId": null}}]}