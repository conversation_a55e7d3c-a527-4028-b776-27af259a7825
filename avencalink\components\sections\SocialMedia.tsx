import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getIconComponent } from "@/lib/iconUtils";
import { memo, useCallback, useState } from "react";

interface SocialMediaItem {
  classIcon: string;
  text: string;
  url: string;
}

interface SocialMediaProps {
  socialMedia: SocialMediaItem[];
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "minimal" | "outlined";
}

const SocialMedia = memo(
  ({
    socialMedia,
    className,
    size = "lg",
    variant = "default",
  }: SocialMediaProps) => {
    const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>(
      {}
    );

    const handleSocialClick = useCallback(
      async (url: string, index: number) => {
        if (!url || url === "#") return;

        // Set loading state
        setLoadingStates((prev) => ({ ...prev, [index]: true }));

        try {
          // Add a small delay for better UX feedback
          await new Promise((resolve) => setTimeout(resolve, 200));
          window.open(url, "_blank", "noopener,noreferrer");
        } catch (error) {
          console.error("Error opening social media link:", error);
        } finally {
          // Clear loading state
          setTimeout(() => {
            setLoadingStates((prev) => ({ ...prev, [index]: false }));
          }, 300);
        }
      },
      []
    );

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent, url: string, index: number) => {
        if (event.key === "Enter" || event.key === " ") {
          event.preventDefault();
          handleSocialClick(url, index);
        }
      },
      [handleSocialClick]
    );

    const getSizeClasses = () => {
      switch (size) {
        case "sm":
          return "w-10 h-10";
        case "md":
          return "w-12 h-12";
        case "lg":
        default:
          return "w-16 h-16 sm:w-14 sm:h-14";
      }
    };

    const getIconSizeClasses = () => {
      switch (size) {
        case "sm":
          return "text-base";
        case "md":
          return "text-lg";
        case "lg":
        default:
          return "text-xl sm:text-lg";
      }
    };

    const getVariantClasses = () => {
      switch (variant) {
        case "minimal":
          return "bg-background/80 backdrop-blur-sm border border-border/50 hover:bg-background hover:border-border text-foreground hover:text-primary";
        case "outlined":
          return "bg-transparent border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 text-primary";
        case "default":
        default:
          return "bg-gradient-to-r from-primary to-primary/70 hover:bg-gradient-to-r hover:from-primary/90 hover:to-primary/60 text-primary-foreground";
      }
    };

    if (!socialMedia?.length) {
      return null;
    }

    return (
      <div
        className={cn(
          "flex justify-center items-center flex-wrap gap-3 sm:gap-4 mb-8",
          "animate-fade-in",
          className
        )}
        role="navigation"
        aria-label="Social media links"
      >
        {socialMedia.map((social, index) => {
          const isLoading = loadingStates[index];
          const isDisabled = !social.url || social.url === "#";

          return (
            <Button
              key={`${social.text}-${index}`}
              onClick={() => handleSocialClick(social.url, index)}
              onKeyDown={(e) => handleKeyDown(e, social.url, index)}
              disabled={isDisabled || isLoading}
              size="icon"
              className={cn(
                getSizeClasses(),
                "rounded-full p-0 group relative overflow-hidden",
                "transform transition-all duration-300 ease-spring",
                "shadow-soft hover:shadow-strong",
                "focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2",
                "hover:scale-110 active:scale-95",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
                getVariantClasses(),
                isLoading && "animate-pulse"
              )}
              style={{
                animationDelay: `${index * 0.1}s`,
                animationFillMode: "both",
              }}
              aria-label={`${social.text} - Opens in new tab`}
              title={social.text}
            >
              {/* Loading spinner overlay */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/20 backdrop-blur-sm rounded-full z-20">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                </div>
              )}

              {/* Icon */}
              <span className="sr-only">{social.text}</span>
              {(() => {
                const IconComponent = getIconComponent(social.classIcon);
                return (
                  <IconComponent
                    className={cn(
                      getIconSizeClasses(),
                      "relative z-10 transition-all duration-300",
                      "group-hover:scale-110 group-active:scale-95",
                      isLoading && "opacity-50"
                    )}
                    aria-hidden="true"
                  />
                );
              })()}

              {/* Hover effect overlay */}
              <div
                className={cn(
                  "absolute inset-0 rounded-full transition-opacity duration-300",
                  "opacity-0 group-hover:opacity-100",
                  variant === "default"
                    ? "bg-gradient-to-r from-white/10 via-white/20 to-white/10"
                    : "bg-gradient-to-r from-primary/10 via-primary/20 to-primary/10"
                )}
              />

              {/* Ripple effect */}
              <div
                className={cn(
                  "absolute inset-0 rounded-full transition-transform duration-150",
                  "scale-0 group-active:scale-100",
                  variant === "default" ? "bg-white/20" : "bg-primary/20"
                )}
              />

              {/* Shine effect on hover */}
              <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute top-0 left-0 w-full h-full rounded-full bg-linear-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              </div>
            </Button>
          );
        })}
      </div>
    );
  }
);

SocialMedia.displayName = "SocialMedia";

export default SocialMedia;
